GEM
  remote: https://rubygems.org/
  specs:
    action_policy (0.7.3)
      ruby-next-core (>= 1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.4)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    active_storage_validations (1.4.0)
      activejob (>= 6.1.4)
      activemodel (>= 6.1.4)
      activestorage (>= 6.1.4)
      activesupport (>= 6.1.4)
      marcel (>= 1.0.3)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    aws-eventstream (1.3.0)
    aws-partitions (1.1031.0)
    aws-sdk-core (3.214.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.96.0)
      aws-sdk-core (~> 3, >= 3.210.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.177.0)
      aws-sdk-core (~> 3, >= 3.210.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.10.1)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    bigdecimal (3.1.9)
    bindex (0.8.1)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    builder (3.3.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    capybara-playwright-driver (0.5.6)
      addressable
      capybara
      playwright-ruby-client (>= 1.16.0)
    concurrent-ruby (1.3.4)
    crass (1.0.6)
    csv (3.3.2)
    cuprite (0.17)
      capybara (~> 3.0)
      ferrum (~> 0.17.0)
    database_cleaner-active_record (2.2.1)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    debug (1.10.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-i18n (1.12.1)
      devise (>= 4.9.0)
    devise_invitable (2.0.9)
      actionmailer (>= 5.0)
      devise (>= 4.6)
    diff-lcs (1.5.1)
    dry-cli (1.2.0)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    factory_bot (6.5.3)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    ferrum (0.17.1)
      addressable (~> 2.5)
      base64 (~> 0.2)
      concurrent-ruby (~> 1.1)
      webrick (~> 1.7)
      websocket-driver (~> 0.7)
    ffi (1.15.5)
    foreman (0.88.1)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    geocoder (1.8.5)
      base64 (>= 0.1.0)
      csv (>= 3.0.0)
    glob (0.4.1)
    globalid (1.2.1)
      activesupport (>= 6.1)
    good_job (4.10.2)
      activejob (>= 6.1.0)
      activerecord (>= 6.1.0)
      concurrent-ruby (>= 1.3.1)
      fugit (>= 1.11.0)
      railties (>= 6.1.0)
      thor (>= 1.0.0)
    heapy (0.2.0)
      thor
    heroicon (1.0.0)
      rails (>= 5.2)
    httparty (0.22.0)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.6)
      concurrent-ruby (~> 1.0)
    i18n-js (4.2.3)
      glob (>= 0.4.0)
      i18n
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    io-console (0.8.0)
    irb (1.14.3)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jmespath (1.6.2)
    jwt (2.10.1)
      base64
    logger (1.6.4)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    memory_profiler (1.1.0)
    method_source (1.1.0)
    mime-types (3.7.0)
      logger
      mime-types-data (~> 3.2025, >= 3.2025.0507)
    mime-types-data (3.2025.0701)
    mini_magick (5.2.0)
      benchmark
      logger
    mini_mime (1.1.5)
    mini_portile2 (2.8.8)
    minitest (5.25.4)
    msgpack (1.7.5)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    mutex_m (0.3.0)
    net-imap (0.5.5)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.0)
      net-protocol
    nio4r (2.7.4)
    nokogiri (1.18.1)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.1-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.1-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.1-x86_64-linux-gnu)
      racc (~> 1.4)
    orm_adapter (0.5.0)
    pagy (9.3.3)
    pg (1.5.9)
    phonelib (0.10.5)
    playwright-ruby-client (1.52.0)
      concurrent-ruby (>= 1.1.6)
      mime-types (>= 3.0)
    psych (5.2.2)
      date
      stringio
    public_suffix (6.0.2)
    puma (5.6.9)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (2.2.10)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-mini-profiler (3.3.1)
      rack (>= 1.2.0)
    rack-proxy (0.7.7)
      rack
    rack-test (2.2.0)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails-i18n (7.0.10)
      i18n (>= 0.7, < 2)
      railties (>= 6.0.0, < 8)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rake (13.2.1)
    rdoc (6.10.0)
      psych (>= 4.0.0)
    regexp_parser (2.10.0)
    reline (0.6.0)
      io-console (~> 0.5)
    resend (0.15.0)
      httparty (>= 0.21.0)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rspec-core (3.13.2)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.1.0)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.2)
    ruby-next-core (1.0.3)
    ruby-vips (2.2.4)
      ffi (~> 1.12)
      logger
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    sentry-rails (5.26.0)
      railties (>= 5.0)
      sentry-ruby (~> 5.26.0)
    sentry-ruby (5.26.0)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    stackprof (0.2.27)
    stringio (3.1.2)
    stripe (13.5.1)
    thor (1.3.2)
    tilt (2.5.0)
    timeout (0.4.3)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    vite_rails (3.0.19)
      railties (>= 5.1, < 9)
      vite_ruby (~> 3.0, >= 3.2.2)
    vite_ruby (3.9.1)
      dry-cli (>= 0.7, < 2)
      logger (~> 1.6)
      mutex_m
      rack-proxy (~> 0.6, >= 0.6.1)
      zeitwerk (~> 2.2)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webrick (1.9.1)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.18)

PLATFORMS
  arm64-darwin
  ruby
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  action_policy (~> 0.7.3)
  active_storage_validations
  aws-sdk-s3 (~> 1.177)
  bootsnap
  capybara
  capybara-playwright-driver
  cuprite
  database_cleaner-active_record
  debug
  devise (~> 4.9)
  devise-i18n
  devise_invitable
  factory_bot_rails
  foreman
  geocoder
  good_job
  heapy
  heroicon
  i18n-js (~> 4.2)
  image_processing (~> 1.2)
  jwt (~> 2.7)
  memory_profiler
  pagy (~> 9.3)
  pg (~> 1.1)
  phonelib
  puma (~> 5.0)
  rack-attack
  rack-mini-profiler
  rails (~> 7.0.8, >= *******)
  rails-i18n
  resend (= 0.15.0)
  rspec-rails (~> 7.1)
  sassc-rails
  sentry-rails
  sentry-ruby
  sprockets-rails
  stackprof
  stripe (~> 13.3)
  tzinfo-data
  vite_rails
  web-console

RUBY VERSION
   ruby 3.1.2p20

BUNDLED WITH
   2.5.23
