# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'AdminAccess', type: :request do
  # Include Devise test helpers for signing in users in request specs
  include Devise::Test::IntegrationHelpers

  let(:admin_user) { create(:user, :super_boss, approved: true) }
  let(:regular_user) { create(:user, approved: true) }

  # --- Test Setup ---
  before do
    # Create complete profiles for both users to satisfy the `ensure_profile_complete` filter
    create(:user_profile, user: admin_user, first_name: 'Admin', last_name: 'User', profile_completed: true)
    create(:user_profile, user: regular_user, first_name: 'Regular', last_name: 'User', profile_completed: true)
    
    # Sign in the admin user for all requests in this spec
    sign_in admin_user
  end

  # --- Tests for GET /admin_access (Project Dashboard) ---
  describe 'GET /admin_access' do
    let!(:draft_project) { create(:project, title: 'Draft Project', user: regular_user, project_status: false, approved: false) }
    let!(:pending_project) { create(:project, title: 'Pending Project', user: regular_user, project_status: true, approved: false) }
    let!(:published_project) do
      # Set is_admin_approval_action to true to bypass the before_save callback that resets `approved` status
      create(:project, title: 'Published Project', user: regular_user, project_status: true, approved: true, admin_approver: admin_user, is_admin_approval_action: true)
    end

    context 'without a status filter' do
      it 'displays all projects' do
        get user_admin_dashboard_path
        
        expect(response).to have_http_status(:ok)
        expect(response.body).to include(draft_project.title)
        expect(response.body).to include(pending_project.title)
        expect(response.body).to include(published_project.title)
      end
    end

    context 'with the "draft" status filter' do
      it 'displays only draft projects' do
        get user_admin_dashboard_path(status: 'draft')
        
        expect(response).to have_http_status(:ok)
        expect(response.body).to include(draft_project.title)
        expect(response.body).not_to include(pending_project.title)
        expect(response.body).not_to include(published_project.title)
      end
    end

    context 'with the "pending" status filter' do
      it 'displays only pending projects' do
        get user_admin_dashboard_path(status: 'pending')
        
        expect(response).to have_http_status(:ok)
        expect(response.body).not_to include(draft_project.title)
        expect(response.body).to include(pending_project.title)
        expect(response.body).not_to include(published_project.title)
      end
    end

    context 'with the "published" status filter' do
      it 'displays only published projects' do
        get user_admin_dashboard_path(status: 'published')
        
        expect(response).to have_http_status(:ok)
        expect(response.body).not_to include(draft_project.title)
        expect(response.body).not_to include(pending_project.title)
        expect(response.body).to include(published_project.title)
      end
    end
  end

  # --- Tests for DELETE /admin_access/destroy_project/:id ---
  describe 'DELETE /admin_access/destroy_project/:id' do
    let!(:project_to_delete) { create(:project, user: regular_user) }

    it 'deletes the project and redirects to the admin dashboard' do
      expect {
        delete admin_destroy_project_path(project_to_delete)
      }.to change(Project, :count).by(-1)

      expect(response).to redirect_to(user_admin_dashboard_path)
      follow_redirect!
      
      expect(flash[:notice]).to eq('Project was successfully deleted.')
    end

    it 'handles a non-existent project gracefully' do
      # The route helper would fail with a record not found before the request,
      # so we construct the path manually for a non-existent ID.
      non_existent_project_path = "/admin_access/projects/99999"
      
      expect {
        delete non_existent_project_path
      }.not_to change(Project, :count)

      expect(response).to redirect_to(user_admin_dashboard_path)
      follow_redirect!
      
      expect(flash[:alert]).to eq('Project not found.')
    end
  end
end
