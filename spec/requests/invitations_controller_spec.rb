require 'rails_helper'

RSpec.describe InvitationsController, type: :request do
  let(:user) { create(:user) }
  let(:existing_user) { create(:user, email: '<EMAIL>') }
  
  before do
    sign_in user
    # Ensure user profile is complete to avoid redirect to profile edit
    unless user.user_profile.first_name.present? && user.user_profile.city.present?
      user.user_profile.update!(
        first_name: 'Test',
        last_name: 'User', 
        city: 'Bratislava',
        country: 'Slovakia'
      )
    end
  end

  describe "GET /invitations" do
    it "renders successfully" do
      get "/sk/invitations"
      expect(response).to be_successful
    end
  end

  describe "POST /invitations" do
    context "when inviting an existing user" do
      it "creates a connection request and shows translated success message in Slovak" do
        expect {
          post "/sk/invitations", params: { 
            email: existing_user.email,
            langlocale: 'sk'
          }
        }.to change(ConnectionRequest, :count).by(1)
        
        expect(response).to redirect_to(network_connections_path)
        expect(flash[:notice]).to eq("Pozvánka bola odoslaná!")
      end
      
      it "creates a connection request and shows translated success message in English" do
        expect {
          post "/en/invitations", params: { 
            email: existing_user.email,
            langlocale: 'en'
          }
        }.to change(ConnectionRequest, :count).by(1)
        
        expect(response).to redirect_to(network_connections_path)
        expect(flash[:notice]).to eq("Invitation sent!")
      end
    end

    context "when inviting a new user" do
      let(:new_email) { '<EMAIL>' }
      
      it "sends invitation and shows translated success message in Slovak" do
        expect {
          post "/sk/invitations", params: { 
            email: new_email,
            langlocale: 'sk'
          }
        }.to change(User, :count).by(1)
        
        expect(response).to redirect_to(network_connections_path)
        expect(flash[:notice]).to eq("Pozvánka bola odoslaná!")
        
        # Verify the invited user was created
        invited_user = User.find_by(email: new_email)
        expect(invited_user).to be_present
        expect(invited_user.invited_by).to eq(user)
      end
      
      it "sends invitation and shows translated success message in English" do
        expect {
          post "/en/invitations", params: { 
            email: new_email,
            langlocale: 'en'
          }
        }.to change(User, :count).by(1)
        
        expect(response).to redirect_to(network_connections_path)
        expect(flash[:notice]).to eq("Invitation sent!")
        
        # Verify the invited user was created
        invited_user = User.find_by(email: new_email)
        expect(invited_user).to be_present
        expect(invited_user.invited_by).to eq(user)
      end
      
      it "sends invitation and shows translated success message in Czech" do
        expect {
          post "/cs/invitations", params: { 
            email: new_email,
            langlocale: 'cs'
          }
        }.to change(User, :count).by(1)
        
        expect(response).to redirect_to(network_connections_path)
        expect(flash[:notice]).to eq("Pozvánka byla odeslána!")
        
        # Verify the invited user was created
        invited_user = User.find_by(email: new_email)
        expect(invited_user).to be_present
        expect(invited_user.invited_by).to eq(user)
      end
    end

    context "when invitation fails" do
      it "shows error message for invalid email" do
        post "/sk/invitations", params: { 
          email: 'invalid-email',
          langlocale: 'sk'
        }
        
        expect(response).to redirect_to(invitations_path)
        expect(flash[:alert]).to be_present
      end
    end
  end

  describe "locale handling" do
    context "when langlocale parameter is provided" do
      it "uses the specified locale for the invitation email and success message" do
        # Mock the User.invite! method to verify locale is set correctly
        allow(User).to receive(:invite!).and_call_original
        
        post "/sk/invitations", params: { 
          email: '<EMAIL>',
          langlocale: 'sk'
        }
        
        # The invitation should be sent with Slovak locale
        expect(User).to have_received(:invite!)
        expect(flash[:notice]).to eq("Pozvánka bola odoslaná!")
      end
    end
  end
end
