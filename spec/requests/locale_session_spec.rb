require 'rails_helper'

RSpec.describe "Locale Session Management", type: :request do
  include Devise::Test::IntegrationHelpers

  describe "ApplicationController#set_locale" do
    context "when visiting a localized URL" do
      it "stores the locale in session" do
        get "/sk/users/sign_in"
        expect(session[:locale]).to eq("sk")
      end

      it "stores English locale in session" do
        get "/en/users/sign_in"
        expect(session[:locale]).to eq("en")
      end

      it "stores default locale when visiting root" do
        get "/"
        expect(session[:locale]).to eq("sk")
      end
    end

    context "when user has a language preference" do
      let(:user) { create(:user, confirmed_at: Time.current) }

      before do
        user.user_profile.update!(default_language: 'en')
        sign_in user
      end

      it "uses user preference and stores it in session" do
        get "/sk/projects" # Visit Slovak URL but user prefers English
        expect(I18n.locale).to eq(:en)
        expect(session[:locale]).to eq("en")
      end
    end
  end

  describe "RegistrationsController locale preservation" do
    let(:user_params) do
      {
        email: '<EMAIL>',
        password: 'password123',
        password_confirmation: 'password123'
      }
    end

    # Disable email confirmation and reCAPTCHA for these tests to simplify testing
    before do
      allow_any_instance_of(User).to receive(:confirmation_required?).and_return(false)
      # Disable reCAPTCHA verification
      allow_any_instance_of(RegistrationsController).to receive(:verify_recaptcha).and_return(true)
      # Ensure credentials don't trigger reCAPTCHA
      allow(Rails.application.credentials).to receive(:recaptcha).and_return(nil)
    end

    context "when registering on Slovak URL" do
      it "preserves Slovak locale in user profile" do
        # First visit a Slovak page to set the session locale
        get "/sk/users/sign_up"
        expect(session[:locale]).to eq("sk")

        # Now register the user
        expect {
          post "/sk/users", params: { user: user_params }
        }.to change(User, :count).by(1)

        # Check that a user was created
        user = User.find_by(email: user_params[:email])
        expect(user).to be_present

        # Check that the user's profile has Slovak as default language
        expect(user.user_profile.default_language).to eq("sk")
      end
    end

    context "when registering on English URL" do
      it "preserves English locale in user profile" do
        # First visit an English page to set the session locale
        get "/en/users/sign_up"
        expect(session[:locale]).to eq("en")

        # Now register the user
        expect {
          post "/en/users", params: { user: user_params.merge(email: '<EMAIL>') }
        }.to change(User, :count).by(1)

        # Check that the user's profile has English as default language
        user = User.find_by(email: '<EMAIL>')
        expect(user.user_profile.default_language).to eq("en")
      end
    end

    context "when registering with default locale" do
      it "uses default locale in user profile" do
        # Visit registration page with default locale
        get "/sk/users/sign_up"
        expect(session[:locale]).to eq("sk")

        # Now register the user
        expect {
          post "/sk/users", params: { user: user_params.merge(email: '<EMAIL>') }
        }.to change(User, :count).by(1)

        # Check that the user's profile has Slovak (default) as default language
        user = User.find_by(email: '<EMAIL>')
        expect(user.user_profile.default_language).to eq("sk")
      end
    end
  end

  describe "Locale consistency after registration" do
    let(:user_params) do
      {
        email: '<EMAIL>',
        password: 'password123',
        password_confirmation: 'password123'
      }
    end

    before do
      allow_any_instance_of(User).to receive(:confirmation_required?).and_return(false)
      # Disable reCAPTCHA verification
      allow_any_instance_of(RegistrationsController).to receive(:verify_recaptcha).and_return(true)
      allow(Rails.application.credentials).to receive(:recaptcha).and_return(nil)
    end

    it "maintains locale preference after user signs in" do
      # Register user on Slovak URL
      get "/sk/users/sign_up"
      post "/sk/users", params: { user: user_params }

      user = User.find_by(email: user_params[:email])
      expect(user.user_profile.default_language).to eq("sk")

      # Sign out and sign back in
      delete "/sk/users/sign_out"

      # Sign in again - need to confirm the user first
      user.update!(confirmed_at: Time.current)
      post "/sk/users/sign_in", params: {
        user: { email: user.email, password: 'password123' }
      }

      # Visit a page and check that Slovak is used
      get "/projects" # No locale in URL
      if response.redirect?
        follow_redirect! # Should redirect to /sk/projects based on user preference
        expect(request.path).to include("/sk/")
      else
        # If no redirect, check that the locale is set correctly
        expect(I18n.locale).to eq(:sk)
      end
    end
  end
end
