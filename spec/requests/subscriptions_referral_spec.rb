# ABOUTME: Test spec for subscription referral code validation and discount calculation
# ABOUTME: Verifies correct price calculations and response format for referral discounts
require 'rails_helper'

RSpec.describe "Subscriptions Referral", type: :request do
  let(:user) { create(:user) }
  let!(:premium_plan) { Plan.create!(name: "Premium", tier: "premium", interval: "month", price_cents: 999, active: true) }
  
  before do
    sign_in user
  end

  describe "GET /subscriptions/validate_referral" do
    context "with valid referral code" do
      let!(:referral_code) { 
        ReferralCode.create!(
          code: "TEST50", 
          discount_percentage: 50.0, 
          max_uses: 10,
          current_uses: 0,
          duration_months: 12,
          status: "active",
          tier_upgrade_to: "premium",
          created_by: user
        ) 
      }
      
      it "returns correct discount calculation" do
        get "/subscriptions/validate_referral", params: { code: "TEST50" }
        
        json = JSON.parse(response.body)
        
        expect(json["valid"]).to be true
        expect(json["code"]).to eq("TEST50")
        expect(json["original_price"]).to eq(9.99)
        expect(json["final_price"]).to eq(4.995)
        expect(json["discount_percentage"]).to eq(50.0)
        expect(json["currency"]).to eq("$")
        expect(json["message"]).to include("50% off")
      end
      
      it "handles 100% discount correctly" do
        referral_code.update!(discount_percentage: 100.0)
        
        get "/subscriptions/validate_referral", params: { code: "TEST50" }
        
        json = JSON.parse(response.body)
        
        expect(json["valid"]).to be true
        expect(json["final_price"]).to eq(0.0)
        expect(json["discount_percentage"]).to eq(100.0)
      end
    end
    
    context "with invalid referral code" do
      it "returns invalid response" do
        get "/subscriptions/validate_referral", params: { code: "INVALID" }
        
        json = JSON.parse(response.body)
        
        expect(json["valid"]).to be false
        expect(json["message"]).to be_present
      end
    end
    
    context "with blank code" do
      it "returns invalid response" do
        get "/subscriptions/validate_referral", params: { code: "" }
        
        json = JSON.parse(response.body)
        
        expect(json["valid"]).to be false
      end
    end
    
    context "with expired referral code" do
      let!(:expired_code) { 
        ReferralCode.create!(
          code: "EXPIRED", 
          discount_percentage: 25.0, 
          expires_at: 1.day.ago,
          max_uses: 10,
          current_uses: 0,
          duration_months: 12,
          status: "active",
          tier_upgrade_to: "premium",
          created_by: user
        ) 
      }
      
      it "returns invalid response" do
        get "/subscriptions/validate_referral", params: { code: "EXPIRED" }
        
        json = JSON.parse(response.body)
        
        expect(json["valid"]).to be false
      end
    end
  end
end