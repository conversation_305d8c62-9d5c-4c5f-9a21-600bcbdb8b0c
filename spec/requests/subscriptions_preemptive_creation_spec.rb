# ABOUTME: Tests for pre-emptive subscription creation to fix race condition
# ABOUTME: Ensures incomplete subscriptions are created before checkout and updated by webhooks

require 'rails_helper'

RSpec.describe "SubscriptionsPreemptiveCreation", type: :request do
  include Devise::Test::IntegrationHelpers
  
  let(:user) { create(:user) }
  let(:plan) { create(:plan, stripe_price_id: 'price_123', tier: 'standard') }
  
  before do
    # Create a complete user profile
    create(:user_profile,
           user: user,
           first_name: '<PERSON>',
           last_name: 'Doe',
           city: 'New York',
           country: 'USA',
           profile_completed: true)
    
    sign_in user
    
    # Mock Stripe API calls
    allow(Stripe::Price).to receive(:retrieve).and_return(
      double('price', product: 'prod_123')
    )
    allow(Stripe::Product).to receive(:retrieve).and_return(
      double('product', name: 'Test Product')
    )
    allow(Stripe::Customer).to receive(:retrieve).and_raise(
      Stripe::InvalidRequestError.new("No such customer", nil)
    )
    allow(Stripe::Customer).to receive(:create).and_return(
      double('customer', id: 'cus_test123')
    )
  end
  
  describe "POST /subscriptions" do
    context "when creating a checkout session" do
      let(:checkout_session) do
        double('checkout_session', 
               url: 'https://checkout.stripe.com/session_123',
               id: 'cs_test_123')
      end
      
      before do
        allow(Stripe::Checkout::Session).to receive(:create).and_return(checkout_session)
      end
      
      it "creates an incomplete subscription before redirecting to Stripe" do
        expect {
          post subscriptions_path, params: { price_id: 'price_123' }
        }.to change(Subscription, :count).by(1)
        
        subscription = Subscription.last
        expect(subscription.user).to eq(user)
        expect(subscription.plan).to eq(plan)
        expect(subscription.status).to eq('incomplete')
        expect(subscription.payment_provider).to eq('stripe')
      end
      
      it "passes the subscription ID as client_reference_id to Stripe" do
        expect(Stripe::Checkout::Session).to receive(:create) do |params|
          expect(params[:client_reference_id]).to match(/^\d+$/) # Should be subscription ID
          expect(params[:metadata][:subscription_id]).to match(/^\d+$/)
          checkout_session
        end
        
        post subscriptions_path, params: { price_id: 'price_123' }
      end
      
      it "cleans up the subscription if Stripe checkout session creation fails" do
        allow(Stripe::Checkout::Session).to receive(:create).and_raise(
          Stripe::StripeError.new("Payment error")
        )
        
        expect {
          post subscriptions_path, params: { price_id: 'price_123' }
        }.not_to change(Subscription, :count)
        
        expect(response).to redirect_to(subscriptions_path)
        expect(flash[:alert]).to include("Payment error")
      end
    end
  end
  
  describe "GET /subscriptions/success" do
    let(:subscription) { create(:subscription, user: user, status: :incomplete) }
    let(:checkout_session) do
      double('checkout_session',
             client_reference_id: subscription.id.to_s,
             subscription: 'sub_123')
    end
    
    before do
      allow(Stripe::Checkout::Session).to receive(:retrieve).and_return(checkout_session)
    end
    
    context "when subscription is still incomplete" do
      it "renders the processing_subscription view" do
        get success_subscriptions_path, params: { session_id: 'cs_test_123' }
        
        expect(response).to have_http_status(:success)
        expect(response.body).to include("Payment Successful")
        expect(response.body).to include("activating your subscription")
      end
      
      it "shows processing message to user" do
        get success_subscriptions_path, params: { session_id: 'cs_test_123' }
        
        expect(flash[:notice]).to include("Payment successful")
        expect(flash[:notice]).to include("activating")
      end
    end
    
    context "when webhook was fast and subscription is already active" do
      before do
        subscription.update!(status: :active)
      end
      
      it "redirects to subscriptions page with success message" do
        get success_subscriptions_path, params: { session_id: 'cs_test_123' }
        
        expect(response).to redirect_to(subscriptions_path)
        expect(flash[:notice]).to be_present
      end
    end
  end
  
  describe "GET /subscriptions/:id/check_status" do
    let(:subscription) { create(:subscription, user: user, status: :incomplete) }
    
    context "when user owns the subscription" do
      it "returns subscription status as JSON" do
        get check_status_subscription_path(subscription), headers: { 'Accept' => 'application/json' }
        
        expect(response).to have_http_status(:success)
        json = JSON.parse(response.body)
        expect(json['id']).to eq(subscription.id)
        expect(json['status']).to eq('incomplete')
        expect(json['can_access_features']).to be false
      end
      
      context "when subscription becomes active" do
        before do
          subscription.update!(status: :active)
        end
        
        it "returns can_access_features as true" do
          get check_status_subscription_path(subscription), headers: { 'Accept' => 'application/json' }
          
          json = JSON.parse(response.body)
          expect(json['can_access_features']).to be true
        end
      end
    end
    
    context "when user does not own the subscription" do
      let(:other_user) { create(:user) }
      let(:other_subscription) { create(:subscription, user: other_user) }
      
      it "returns not found error to prevent information leakage" do
        get check_status_subscription_path(other_subscription), headers: { 'Accept' => 'application/json' }
        
        expect(response).to have_http_status(:not_found)
        json = JSON.parse(response.body)
        expect(json['error']).to eq('Not Found')
      end
    end
    
    context "when subscription ID does not exist" do
      it "returns not found error" do
        get check_status_subscription_path(id: 99999), headers: { 'Accept' => 'application/json' }
        
        expect(response).to have_http_status(:not_found)
        json = JSON.parse(response.body)
        expect(json['error']).to eq('Not Found')
      end
    end
  end
end