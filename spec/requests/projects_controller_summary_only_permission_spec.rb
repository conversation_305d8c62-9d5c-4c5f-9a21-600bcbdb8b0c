# ABOUTME: Tests for ProjectsController#ensure_summary_only_permission method  
# ABOUTME: Verifies proper instance variable initialization when permission denied

require 'rails_helper'

RSpec.describe "ProjectsController Summary Only Permission", type: :request do
  let(:admin_user) { create(:user, :super_boss) }  # Super boss users have paid subscription features
  let(:free_user) { create(:user) }  # Regular user without subscription
  let(:project) { create(:project, user: free_user, project_status: false) }  # Draft project

  describe "PATCH /projects/:id - updating summary_only setting" do
    context "autosave scenarios (Rails checkbox sends '0'/'1')" do
      before do
        sign_in free_user
      end

      it "does not trigger permission check when checkbox value unchanged (autosave with '0')" do
        # Project has summary_only = false, autosave sends "0" (unchecked)
        project.update!(summary_only: false)
        
        patch project_path(project), params: {
          project: {
            summary_only: "0",  # Rails checkbox sends "0" for unchecked
            title: "Updated via autosave"
          }
        }

        # Should successfully save without triggering permission check
        expect(response).to redirect_to(edit_project_path(project))
        expect(flash[:alert]).to be_nil
        project.reload
        expect(project.title).to eq("Updated via autosave")
        expect(project.summary_only).to eq(false)
      end

      it "does not trigger permission check when checkbox value unchanged (autosave with '1')" do
        # Admin sets summary_only = true
        project.update!(summary_only: true, user: admin_user)
        project.update!(user: free_user)  # Transfer back to free user
        
        patch project_path(project), params: {
          project: {
            summary_only: "1",  # Rails checkbox sends "1" for checked
            title: "Another autosave update"
          }
        }

        # Should successfully save without triggering permission check
        expect(response).to redirect_to(edit_project_path(project))
        expect(flash[:alert]).to be_nil
        project.reload
        expect(project.title).to eq("Another autosave update")
        expect(project.summary_only).to eq(true)
      end

      it "does not trigger permission check when empty string sent" do
        # Empty string can be sent in certain form submission scenarios
        project.update!(summary_only: false)
        
        patch project_path(project), params: {
          project: {
            summary_only: "",  # Empty string should not trigger change detection
            title: "Empty string test"
          }
        }

        # Should successfully save without triggering permission check
        expect(response).to redirect_to(edit_project_path(project))
        expect(flash[:alert]).to be_nil
        project.reload
        expect(project.title).to eq("Empty string test")
        expect(project.summary_only).to eq(false)  # Should remain unchanged
      end
    end
    context "when user lacks can_create_summary_only_projects permission" do
      before do
        sign_in free_user
      end

      it "renders edit view with proper instance variables when permission denied" do
        # Create some test data
        auth_user = create(:user)
        project_auth = create(:project_auth, 
          project: project, 
          user: auth_user, 
          access_level: :full_details
        )

        # Attempt to update summary_only setting without permission
        patch project_path(project), params: {
          project: {
            summary_only: "true",
            title: project.title
          }
        }

        # Should return unprocessable entity status
        expect(response).to have_http_status(:unprocessable_entity)
        
        # Should display the permission error message
        expect(flash[:alert]).to eq('Upgrade required to use summary-only projects')
        
        # Verify @cached_translations is properly initialized (no nil error)
        # The key check is that we don't get "undefined method '[]' for nil:NilClass"
        expect(response.body).to include("projectTypes")
        
        # Verify the form renders without errors
        expect(response.body).not_to include("undefined method")
        expect(response.body).not_to include("for nil:NilClass")
      end

      it "does not update the project's summary_only setting" do
        original_value = project.summary_only
        
        patch project_path(project), params: {
          project: {
            summary_only: (!original_value).to_s,
            title: project.title
          }
        }

        project.reload
        expect(project.summary_only).to eq(original_value)
      end
    end

    context "when user has can_create_summary_only_projects permission" do
      before do
        sign_in admin_user
        # Transfer ownership to admin user for testing
        project.update!(user: admin_user)
      end

      it "successfully updates the summary_only setting" do
        original_value = project.summary_only
        new_value = !original_value
        
        patch project_path(project), params: {
          project: {
            summary_only: new_value.to_s,
            title: project.title,
            status: project.status
          }
        }

        expect(response).to redirect_to(project_path(project))
        
        project.reload
        expect(project.summary_only).to eq(new_value)
      end
    end

    context "when not changing summary_only setting" do
      before do
        sign_in free_user
      end

      it "allows update of other fields without permission check" do
        new_title = "Updated Title #{SecureRandom.hex(4)}"
        
        patch project_path(project), params: {
          project: {
            title: new_title,
            status: project.status
          }
        }

        expect(response).to redirect_to(project_path(project))
        
        project.reload
        expect(project.title).to eq(new_title)
      end
    end
  end

  describe "edge cases" do
    before do
      sign_in free_user
    end

    it "handles nil params gracefully" do
      patch project_path(project), params: {
        project: {
          title: "New Title"
        }
      }

      expect(response).to redirect_to(project_path(project))
    end

    it "handles empty string summary_only param" do
      patch project_path(project), params: {
        project: {
          summary_only: "",
          title: project.title
        }
      }

      expect(response).to redirect_to(project_path(project))
    end

    it "properly initializes all required instance variables for complex projects" do
      # Create complex project with multiple associations
      5.times do
        create(:project_auth, project: project, access_level: :full_details)
      end

      patch project_path(project), params: {
        project: {
          summary_only: "true",
          title: project.title
        }
      }

      expect(response).to have_http_status(:unprocessable_entity)
      expect(response.body).not_to include("undefined method")
      
      # Verify the edit form renders completely
      expect(response.body).to include("form")
      expect(response.body).to include("project[title]")
    end
  end
end