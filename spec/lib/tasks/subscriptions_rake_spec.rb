# ABOUTME: RSpec tests for subscriptions rake tasks including manual subscription creation
# ABOUTME: Validates task behavior, error handling, and data integrity for bulk operations
require 'rails_helper'
require 'rake'

RSpec.describe 'subscriptions:create_manual_yearly' do
  before(:all) do
    Rails.application.load_tasks if Rake::Task.tasks.empty?
  end

  let(:task) { Rake::Task['subscriptions:create_manual_yearly'] }
  let!(:plan) { create(:plan, name: 'Standard Yearly', tier: :standard, interval: :year, price_cents: 9900) }
  
  before do
    task.reenable
  end

  describe 'when plan exists' do
    let!(:users) { create_list(:user, 3) }

    context 'with users having no existing subscriptions' do
      it 'creates manual subscriptions for all users' do
        expect { task.invoke }.to change { Subscription.count }.by(3)
        
        users.each do |user|
          subscription = user.subscriptions.last
          expect(subscription).to have_attributes(
            plan: plan,
            status: 'active',
            payment_provider: 'manual'
          )
          expect(subscription.current_period_start).to be_within(1.minute).of(Time.current)
          expect(subscription.current_period_end).to be_within(1.minute).of(1.year.from_now)
        end
      end

      it 'outputs success messages' do
        expect { task.invoke }.to output(/Successfully created manual subscription/).to_stdout
      end
    end

    context 'with users having existing active subscriptions to the same plan' do
      before do
        users.first.subscriptions.create!(
          plan: plan,
          status: :active,
          payment_provider: :manual,
          current_period_start: Time.current,
          current_period_end: 1.year.from_now
        )
      end

      it 'skips users with existing active subscriptions' do
        expect { task.invoke }.to change { Subscription.count }.by(2)
                                   .and output(/already has an active subscription/).to_stdout
      end

      it 'does not duplicate subscriptions' do
        task.invoke
        expect(users.first.subscriptions.where(plan: plan, status: :active).count).to eq(1)
      end
    end

    context 'with users having canceled subscriptions to the same plan' do
      before do
        users.first.subscriptions.create!(
          plan: plan,
          status: :canceled,
          payment_provider: :stripe,
          current_period_start: 1.month.ago,
          current_period_end: 11.months.from_now,
          canceled_at: Time.current
        )
      end

      it 'creates new subscription for users with canceled subscriptions' do
        expect { task.invoke }.to change { Subscription.count }.by(3)
        
        # User should now have both canceled and active subscriptions
        expect(users.first.subscriptions.count).to eq(2)
        expect(users.first.subscriptions.where(status: :active).count).to eq(1)
      end
    end

    context 'with save validation bypass' do
      it 'saves subscriptions without running validations' do
        # Create an invalid user state that would normally fail validations
        user_with_issue = users.first
        
        # Monkey-patch to test validation bypass
        allow_any_instance_of(Subscription).to receive(:save).with(validate: false).and_return(true)
        
        expect { task.invoke }.not_to raise_error
      end
    end
  end

  describe 'when plan does not exist' do
    before do
      plan.destroy
    end

    it 'outputs error message and exits gracefully' do
      expect { task.invoke }.to output(/Error: Plan 'Standard Yearly' not found/).to_stdout
      expect { task.invoke }.not_to change { Subscription.count }
    end

    it 'does not create any subscriptions' do
      create_list(:user, 3)
      expect { task.invoke }.not_to change { Subscription.count }
    end
  end

  describe 'when no users exist' do
    it 'completes without errors' do
      expect { task.invoke }.to output(/Subscription creation process finished/).to_stdout
      expect { task.invoke }.not_to raise_error
    end
  end

  describe 'with mixed user scenarios' do
    let!(:user_with_stripe) { create(:user) }
    let!(:user_with_manual) { create(:user) }
    let!(:user_without_subscription) { create(:user) }
    let!(:other_plan) { create(:plan, name: 'Premium Monthly', tier: :standard, interval: :month) }

    before do
      # User with Stripe subscription to same plan
      user_with_stripe.subscriptions.create!(
        plan: plan,
        status: :active,
        payment_provider: :stripe,
        stripe_subscription_id: 'sub_123',
        current_period_start: Time.current,
        current_period_end: 1.year.from_now
      )

      # User with manual subscription to different plan
      user_with_manual.subscriptions.create!(
        plan: other_plan,
        status: :active,
        payment_provider: :manual,
        current_period_start: Time.current,
        current_period_end: 1.month.from_now
      )
    end

    it 'handles mixed scenarios correctly' do
      expect { task.invoke }.to change { Subscription.count }.by(2)
      
      # User with existing subscription to same plan should be skipped
      expect(user_with_stripe.subscriptions.where(plan: plan).count).to eq(1)
      
      # User with subscription to different plan should get new subscription
      expect(user_with_manual.subscriptions.where(plan: plan).count).to eq(1)
      
      # User without any subscription should get new subscription
      expect(user_without_subscription.subscriptions.where(plan: plan).count).to eq(1)
    end
  end
end