# ABOUTME: Test suite for EmailDeliveryTracker model to verify tracking functionality
# ABOUTME: Ensures proper tracking of email delivery status and idempotency

require 'rails_helper'

RSpec.describe EmailDeliveryTracker, type: :model do
  let(:project) { create(:project) }
  let(:user) { create(:user) }
  
  describe 'validations' do
    it 'requires email_type' do
      tracker = EmailDeliveryTracker.new(project: project, user: user)
      expect(tracker).not_to be_valid
      expect(tracker.errors[:email_type]).not_to be_empty  # Just check there's an error
    end
    
    it 'validates status inclusion' do
      tracker = EmailDeliveryTracker.new(
        project: project,
        user: user,
        email_type: 'test',
        status: 'invalid'
      )
      expect(tracker).not_to be_valid
      expect(tracker.errors[:status]).not_to be_empty  # Just check there's an error
    end
    
    it 'accepts valid statuses' do
      %w[pending queued sent failed].each do |status|
        tracker = EmailDeliveryTracker.new(
          project: project,
          user: user,
          email_type: 'test',
          status: status
        )
        expect(tracker).to be_valid
      end
    end
  end
  
  describe 'scopes' do
    let!(:pending_tracker) { create_tracker(status: 'pending') }
    let!(:sent_tracker) { create_tracker(status: 'sent', user: create(:user)) }
    let!(:failed_tracker) { create_tracker(status: 'failed', user: create(:user)) }
    let!(:stuck_tracker) { create_tracker(status: 'pending', created_at: 2.hours.ago, user: create(:user)) }
    
    it '.pending returns pending trackers' do
      expect(EmailDeliveryTracker.pending).to include(pending_tracker, stuck_tracker)
      expect(EmailDeliveryTracker.pending).not_to include(sent_tracker, failed_tracker)
    end
    
    it '.sent returns sent trackers' do
      expect(EmailDeliveryTracker.sent).to include(sent_tracker)
      expect(EmailDeliveryTracker.sent).not_to include(pending_tracker, failed_tracker)
    end
    
    it '.failed returns failed trackers' do
      expect(EmailDeliveryTracker.failed).to include(failed_tracker)
      expect(EmailDeliveryTracker.failed).not_to include(pending_tracker, sent_tracker)
    end
    
    it '.stuck returns old pending trackers' do
      expect(EmailDeliveryTracker.stuck).to include(stuck_tracker)
      expect(EmailDeliveryTracker.stuck).not_to include(pending_tracker)
    end
  end
  
  describe '#sent?' do
    it 'returns true when sent_at is present' do
      tracker = create_tracker(sent_at: 1.hour.ago)
      expect(tracker.sent?).to be true
    end
    
    it 'returns true when status is sent' do
      tracker = create_tracker(status: 'sent')
      expect(tracker.sent?).to be true
    end
    
    it 'returns false when not sent' do
      tracker = create_tracker(status: 'pending')
      expect(tracker.sent?).to be false
    end
  end
  
  describe '#mark_as_queued!' do
    it 'updates status to queued without setting sent_at' do
      tracker = create_tracker(status: 'pending')
      
      expect {
        tracker.mark_as_queued!
      }.to change { tracker.status }.from('pending').to('queued')
      
      expect(tracker.sent_at).to be_nil  # sent_at should only be set when actually sent
    end
  end
  
  describe '#mark_as_sent!' do
    it 'updates status and sent_at' do
      tracker = create_tracker(status: 'queued')
      
      expect {
        tracker.mark_as_sent!
      }.to change { tracker.status }.from('queued').to('sent')
      
      expect(tracker.sent_at).to be_present
    end
  end
  
  describe '#mark_as_failed!' do
    it 'updates status and increments retry_count' do
      tracker = create_tracker(status: 'queued', retry_count: 1)
      
      expect {
        tracker.mark_as_failed!('API error')
      }.to change { tracker.retry_count }.from(1).to(2)
      
      expect(tracker.status).to eq('failed')
    end
    
    it 'logs error message' do
      tracker = create_tracker
      
      expect(Rails.logger).to receive(:error).with(/Email delivery failed/)
      
      tracker.mark_as_failed!('Test error')
    end
  end
  
  describe 'uniqueness' do
    it 'enforces unique combination of project, user, and email_type' do
      create_tracker
      
      duplicate = EmailDeliveryTracker.new(
        project: project,
        user: user,
        email_type: 'new_project_notification'
      )
      
      expect { duplicate.save! }.to raise_error(ActiveRecord::RecordNotUnique)
    end
    
    it 'allows same user for different projects' do
      create_tracker
      other_project = create(:project)
      
      tracker = EmailDeliveryTracker.new(
        project: other_project,
        user: user,
        email_type: 'new_project_notification'
      )
      
      expect(tracker).to be_valid
    end
  end
  
  private
  
  def create_tracker(attrs = {})
    EmailDeliveryTracker.create!(
      {
        project: project,
        user: user,
        email_type: 'new_project_notification',
        status: 'pending'
      }.merge(attrs)
    )
  end
end