# ABOUTME: RSpec tests for Plan model validating subscription tiers and pricing logic
# ABOUTME: Tests cover validations, associations, scopes, and business logic methods
require 'rails_helper'

RSpec.describe Plan, type: :model do
  describe 'associations' do
    it 'has many subscriptions' do
      expect(Plan.reflect_on_association(:subscriptions)).to be_present
      expect(Plan.reflect_on_association(:subscriptions).macro).to eq(:has_many)
    end
    
    it 'has many users through subscriptions' do
      expect(Plan.reflect_on_association(:users)).to be_present
      expect(Plan.reflect_on_association(:users).macro).to eq(:has_many)
      expect(Plan.reflect_on_association(:users).options[:through]).to eq(:subscriptions)
    end
  end

  describe 'validations' do
    let(:valid_plan) { Plan.new(name: 'Test Plan', tier: 'free', interval: 'month', price_cents: 0) }
    
    it 'validates presence of name' do
      plan = Plan.new(tier: 'free', interval: 'month', price_cents: 0)
      expect(plan).not_to be_valid
      expect(plan.errors[:name]).not_to be_empty
    end
    
    it 'validates uniqueness of name' do
      Plan.create!(name: 'Unique Plan', tier: 'free', interval: 'month', price_cents: 0)
      duplicate = Plan.new(name: 'Unique Plan', tier: 'premium', interval: 'year', price_cents: 999)
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:name]).not_to be_empty
    end
    
    it 'validates presence of tier' do
      plan = Plan.new(name: 'Test Plan', interval: 'month', price_cents: 0)
      expect(plan).not_to be_valid
      expect(plan.errors[:tier]).not_to be_empty
    end
    
    it 'validates presence of interval' do
      plan = Plan.new(name: 'Test Plan', tier: 'free', price_cents: 0)
      expect(plan).not_to be_valid
      expect(plan.errors[:interval]).not_to be_empty
    end
    
    it 'validates presence of price_cents' do
      plan = Plan.new(name: 'Test Plan', tier: 'free', interval: 'month')
      plan.valid?
      expect(plan.errors[:price_cents]).to be_empty # price_cents has default value
    end
    
    it 'validates price_cents is non-negative' do
      plan = Plan.new(name: 'Test Plan', tier: 'free', interval: 'month', price_cents: -100)
      expect(plan).not_to be_valid
      expect(plan.errors[:price_cents]).not_to be_empty
    end
    
    it 'allows valid plan' do
      expect(valid_plan).to be_valid
    end
  end

  describe 'enums' do
    it 'defines tier enum' do
      expect(Plan.tiers).to eq({
        'free' => 0,
        'premium' => 1,
        'pilot' => 2
      })
    end

    it 'defines interval enum' do
      expect(Plan.intervals).to eq({
        'one_time' => 0,
        'month' => 1,
        'year' => 2
      })
    end
  end

  describe 'scopes' do
    let!(:active_plan) { Plan.create!(name: 'Active Plan', tier: 'premium', interval: 'month', price_cents: 999, active: true) }
    let!(:inactive_plan) { Plan.create!(name: 'Inactive Plan', tier: 'premium', interval: 'month', price_cents: 999, active: false) }
    let!(:free_plan) { Plan.create!(name: 'Free Plan', tier: 'free', interval: 'one_time', price_cents: 0) }
    let!(:premium_plan) { Plan.create!(name: 'Premium Plan', tier: 'premium', interval: 'year', price_cents: 9999) }

    describe '.active' do
      it 'returns only active plans' do
        expect(Plan.active).to include(active_plan, free_plan, premium_plan)
        expect(Plan.active).not_to include(inactive_plan)
      end
    end

    describe '.inactive' do
      it 'returns only inactive plans' do
        expect(Plan.inactive).to include(inactive_plan)
        expect(Plan.inactive).not_to include(active_plan, free_plan, premium_plan)
      end
    end

    describe '.by_tier' do
      it 'returns plans by tier' do
        expect(Plan.by_tier('free')).to include(free_plan)
        expect(Plan.by_tier('free')).not_to include(premium_plan)
        expect(Plan.by_tier('premium')).to include(premium_plan, active_plan, inactive_plan)
      end
    end

    describe '.free_plans' do
      it 'returns only free plans' do
        expect(Plan.free_plans).to include(free_plan)
        expect(Plan.free_plans).not_to include(premium_plan)
      end
    end

    describe '.paid_plans' do
      it 'returns only paid plans' do
        expect(Plan.paid_plans).to include(premium_plan, active_plan, inactive_plan)
        expect(Plan.paid_plans).not_to include(free_plan)
      end
    end
  end

  describe 'instance methods' do
    describe '#free?' do
      it 'returns true for free tier' do
        plan = Plan.new(tier: 'free')
        expect(plan.free?).to be true
      end

      it 'returns false for non-free tiers' do
        plan = Plan.new(tier: 'premium')
        expect(plan.free?).to be false
      end
    end

    describe '#paid?' do
      it 'returns true for non-free tiers' do
        plan = Plan.new(tier: 'premium')
        expect(plan.paid?).to be true
      end

      it 'returns false for free tier' do
        plan = Plan.new(tier: 'free')
        expect(plan.paid?).to be false
      end
    end

    describe '#display_name' do
      it 'includes name and formatted price' do
        plan = Plan.new(name: 'Premium', tier: 'premium', interval: 'month', price_cents: 999)
        expect(plan.display_name).to eq('Premium - $9.99/month')
      end
    end

    describe '#formatted_price' do
      it 'returns "Free" for zero price' do
        plan = Plan.new(price_cents: 0)
        expect(plan.formatted_price).to eq('Free')
      end

      it 'formats monthly price correctly' do
        plan = Plan.new(price_cents: 999, interval: 'month')
        expect(plan.formatted_price).to eq('$9.99/month')
      end

      it 'formats yearly price correctly' do
        plan = Plan.new(price_cents: 9999, interval: 'year')
        expect(plan.formatted_price).to eq('$99.99/year')
      end

      it 'formats one-time price correctly' do
        plan = Plan.new(price_cents: 29999, interval: 'one_time')
        expect(plan.formatted_price).to eq('$299.99 (one-time)')
      end
    end

    describe '#stripe_configured?' do
      it 'returns true when both stripe fields are present' do
        plan = Plan.new(stripe_price_id: 'price_123', stripe_product_id: 'prod_123')
        expect(plan.stripe_configured?).to be true
      end

      it 'returns false when stripe fields are missing' do
        plan = Plan.new
        expect(plan.stripe_configured?).to be false
      end
    end

    describe '#paddle_configured?' do
      it 'returns true when paddle_price_id is present' do
        plan = Plan.new(paddle_price_id: 'paddle_123')
        expect(plan.paddle_configured?).to be true
      end

      it 'returns false when paddle_price_id is missing' do
        plan = Plan.new
        expect(plan.paddle_configured?).to be false
      end
    end

    describe '#lemonsqueezy_configured?' do
      it 'returns true when lemonsqueezy_variant_id is present' do
        plan = Plan.new(lemonsqueezy_variant_id: 'lemon_123')
        expect(plan.lemonsqueezy_configured?).to be true
      end

      it 'returns false when lemonsqueezy_variant_id is missing' do
        plan = Plan.new
        expect(plan.lemonsqueezy_configured?).to be false
      end
    end
  end

  describe '.find_by_provider_id' do
    let!(:plan) do
      Plan.create!(
        name: 'Multi Provider Plan',
        tier: 'premium',
        interval: 'month',
        price_cents: 999,
        stripe_price_id: 'stripe_123',
        paddle_price_id: 'paddle_123',
        lemonsqueezy_variant_id: 'lemon_123'
      )
    end

    it 'finds plan by stripe_price_id' do
      expect(Plan.find_by_provider_id('stripe_123')).to eq(plan)
    end

    it 'finds plan by paddle_price_id' do
      expect(Plan.find_by_provider_id('paddle_123')).to eq(plan)
    end

    it 'finds plan by lemonsqueezy_variant_id' do
      expect(Plan.find_by_provider_id('lemon_123')).to eq(plan)
    end

    it 'returns nil for non-existent provider id' do
      expect(Plan.find_by_provider_id('nonexistent')).to be_nil
    end
  end
end