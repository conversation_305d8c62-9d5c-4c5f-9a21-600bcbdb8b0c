# ABOUTME: Test suite for DailyNewProjectsDigestJob to verify daily digest email generation
# ABOUTME: Ensures digest emails respect project visibility rules and are sent to eligible users
require 'rails_helper'

RSpec.describe DailyNewProjectsDigestJob, type: :job do
  include ActiveJob::TestHelper
  
  around(:each) do |example|
    clear_enqueued_jobs
    clear_performed_jobs
    example.run
    clear_enqueued_jobs
    clear_performed_jobs
  end
  
  let(:project_owner) { create(:user, approved: true) }
  let(:network_user) { create(:user, approved: true) }
  let(:non_network_user) { create(:user, approved: true) }
  let(:unapproved_user) { create(:user, approved: false) }
  let(:admin_user) { create(:user, role: :super_boss) }
  
  before do
    # Setup user profiles
    [project_owner, network_user, non_network_user, unapproved_user].each do |user|
      create(:user_profile, user: user) unless user.user_profile
    end
    
    # Create network connection between project_owner and network_user
    # (NetworkConnection model handles bidirectionality internally)
    create(:network_connection, 
           inviter: project_owner, 
           invitee: network_user,
           is_accepted: true)
  end
  
  describe '#perform' do
    context 'with network_only projects' do
      before do
        # Create a network_only project
        project = create(:project, 
                        user: project_owner,
                        network_only: true,
                        semi_public: false,
                        created_at: 1.hour.ago,
                        project_status: true,
                        approved: true,
                        admin_approver: admin_user,
                        is_admin_approval_action: true)
      end
      
      it 'sends digest only to users in the project owner network' do
        expect {
          DailyNewProjectsDigestJob.perform_now
        }.to have_enqueued_job(RateLimitedMailDeliveryJob)
          .once # Only network_user should receive it
      end
      
      it 'does not send to users outside the network' do
        DailyNewProjectsDigestJob.perform_now
        
        # Check that non_network_user doesn't get an email
        # The job should only queue one email (for network_user)
        expect(RateLimitedMailDeliveryJob).to have_been_enqueued.once
      end
      
      it 'does not send to unapproved users even if in network' do
        # Add unapproved user to network
        create(:network_connection, 
               inviter: project_owner, 
               invitee: unapproved_user,
               is_accepted: true)
        
        expect {
          DailyNewProjectsDigestJob.perform_now
        }.to have_enqueued_job(RateLimitedMailDeliveryJob)
          .once # Still only network_user (approved) should receive it
      end
    end
    
    context 'with semi_public projects' do
      before do
        # Create a semi_public project
        project = create(:project, 
                        user: project_owner,
                        network_only: false,
                        semi_public: true,
                        created_at: 1.hour.ago,
                        project_status: true,
                        approved: true,
                        admin_approver: admin_user,
                        is_admin_approval_action: true)
      end
      
      it 'sends digest to all active approved users' do
        expect {
          DailyNewProjectsDigestJob.perform_now
        }.to have_enqueued_job(RateLimitedMailDeliveryJob)
          .exactly(3).times # project_owner, network_user, and non_network_user
      end
      
      it 'does not send to unapproved users' do
        DailyNewProjectsDigestJob.perform_now
        
        # Should send to 3 approved users, not the unapproved one
        expect(RateLimitedMailDeliveryJob).to have_been_enqueued.exactly(3).times
      end
    end
    
    context 'with mixed visibility projects' do
      before do
        # Create a network_only project
        network_project = create(:project, 
                                user: project_owner,
                                network_only: true,
                                semi_public: false,
                                created_at: 2.hours.ago,
                                project_status: true,
                                approved: true,
                                admin_approver: admin_user,
                                is_admin_approval_action: true)
        
        # Create a semi_public project
        public_project = create(:project, 
                               user: project_owner,
                               network_only: false,
                               semi_public: true,
                               created_at: 1.hour.ago,
                               project_status: true,
                               approved: true,
                               admin_approver: admin_user,
                               is_admin_approval_action: true)
      end
      
      it 'sends only one email per user regardless of how many projects they can see' do
        expect {
          DailyNewProjectsDigestJob.perform_now
        }.to have_enqueued_job(RateLimitedMailDeliveryJob)
          .exactly(3).times # Each user gets only one email
      end
      
      it 'ensures uniqueness through Set operations' do
        # network_user can see both projects (network + semi_public)
        # but should only get one email
        DailyNewProjectsDigestJob.perform_now
        
        # Total of 3 unique users should receive emails
        expect(RateLimitedMailDeliveryJob).to have_been_enqueued.exactly(3).times
      end
    end
    
    # Note: Projects require either network_only or semi_public to be true (validation enforced)
    # This context is kept for documentation but the validation prevents this scenario
    
    context 'when there are no new projects' do
      it 'does not send any emails' do
        expect {
          DailyNewProjectsDigestJob.perform_now
        }.not_to have_enqueued_job(RateLimitedMailDeliveryJob)
      end
      
      it 'logs that no emails were sent' do
        expect(Rails.logger).to receive(:info).at_least(:once)
        
        DailyNewProjectsDigestJob.perform_now
      end
    end
    
    context 'with only unapproved or unpublished projects' do
      before do
        # Create unapproved project
        unapproved_project = create(:project, 
                                   user: project_owner,
                                   network_only: false,
                                   semi_public: true,
                                   created_at: 1.hour.ago,
                                   approved: false,
                                   project_status: true)
        
        # Create unpublished project
        unpublished_project = create(:project, 
                                    user: project_owner,
                                    network_only: false,
                                    semi_public: true,
                                    created_at: 1.hour.ago,
                                    project_status: false,
                                    approved: true,
                                    admin_approver: admin_user,
                                    is_admin_approval_action: true)
      end
      
      it 'does not send emails for unapproved or unpublished projects' do
        expect {
          DailyNewProjectsDigestJob.perform_now
        }.not_to have_enqueued_job(RateLimitedMailDeliveryJob)
      end
    end
    
    context 'when email sending fails for one user' do
      let(:another_user) { create(:user, approved: true) }
      
      before do
        create(:user_profile, user: another_user)
        
        # Create a semi_public project to notify all users
        project = create(:project, 
                        user: project_owner,
                        network_only: false,
                        semi_public: true,
                        created_at: 1.hour.ago,
                        project_status: true,
                        approved: true,
                        admin_approver: admin_user,
                        is_admin_approval_action: true)
        
        # Mock failure for the first user
        allow(NotificationMailer).to receive(:daily_new_projects_digest)
          .with(project_owner, nil)
          .and_raise(StandardError.new("Email service error"))
        
        allow(NotificationMailer).to receive(:daily_new_projects_digest)
          .with(anything, nil)
          .and_call_original
      end
      
      it 'continues sending to other users' do
        expect {
          DailyNewProjectsDigestJob.perform_now
        }.to have_enqueued_job(RateLimitedMailDeliveryJob)
          .at_least(:twice) # Other users still get emails
      end
      
      it 'logs the error' do
        expect(Rails.logger).to receive(:error)
          .with(/Failed to queue email for user #{project_owner.id}/)
        
        DailyNewProjectsDigestJob.perform_now
      end
    end
  end
end