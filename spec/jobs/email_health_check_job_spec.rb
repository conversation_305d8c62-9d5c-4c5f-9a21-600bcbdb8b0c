# ABOUTME: Test suite for EmailHealthCheckJob to verify monitoring and alerting
# ABOUTME: Ensures health checks properly detect and report email delivery issues

require 'rails_helper'

RSpec.describe EmailHealthCheckJob, type: :job do
  let(:project) { create(:project) }
  let(:user) { create(:user) }
  
  describe '#perform' do
    context 'when no issues detected' do
      it 'logs success message' do
        expect(Rails.logger).to receive(:info).with(/Email health check passed/)
        
        EmailHealthCheckJob.perform_now
      end
    end
    
    context 'when failed deliveries exceed threshold' do
      before do
        # Create 11 failed email trackers
        11.times do |i|
          EmailDeliveryTracker.create!(
            project: project,
            user: create(:user),
            email_type: 'new_project_notification',
            status: 'failed',
            retry_count: 3
          )
        end
      end
      
      it 'logs error alert' do
        expect(Rails.logger).to receive(:error).with(/EMAIL HEALTH CHECK ALERT/)
        
        EmailHealthCheckJob.perform_now
      end
      
      it 'attempts to alert admins' do
        create(:user, role: :super_boss, email: '<EMAIL>')
        
        expect(Rails.logger).to receive(:error).with(/Would send alert to admins: <EMAIL>/)
        
        EmailHealthCheckJob.perform_now
      end
    end
    
    context 'when stuck emails exceed threshold' do
      before do
        # Create 21 stuck email trackers (pending for > 1 hour)
        21.times do |i|
          EmailDeliveryTracker.create!(
            project: project,
            user: create(:user),
            email_type: 'new_project_notification',
            status: 'pending',
            created_at: 2.hours.ago
          )
        end
      end
      
      it 'logs error alert' do
        expect(Rails.logger).to receive(:error).with(/EMAIL HEALTH CHECK ALERT/)
        
        EmailHealthCheckJob.perform_now
      end
    end
    
    it 'logs email delivery statistics' do
      # Create various email trackers
      EmailDeliveryTracker.create!(
        project: project,
        user: user,
        email_type: 'new_project_notification',
        status: 'sent',
        sent_at: 1.hour.ago
      )
      
      EmailDeliveryTracker.create!(
        project: project,
        user: create(:user),
        email_type: 'new_project_notification',
        status: 'pending'
      )
      
      expect(Rails.logger).to receive(:info).with(/Email delivery statistics/)
      
      EmailHealthCheckJob.perform_now
    end
  end
end