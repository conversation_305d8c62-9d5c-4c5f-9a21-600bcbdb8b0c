# ABOUTME: Test suite for RateLimitedMailDeliveryJob to verify email delivery and tracker updates
# ABOUTME: Ensures proper rate limiting, error handling, and EmailDeliveryTracker integration

require 'rails_helper'

RSpec.describe RateLimitedMailDeliveryJob, type: :job do
  let(:user) { create(:user) }
  let(:project) { create(:project, user: user) }
  let(:mailer_class_name) { 'NotificationMailer' }
  let(:delivery_method) { 'deliver_now' }
  
  describe '#perform' do
    context 'when delivering new_project_notification' do
      let(:mailer_method) { 'new_project_notification' }
      let(:delivery_args) do
        {
          args: [project, user],
          kwargs: {},
          params: {}
        }
      end
      
      it 'delivers the email successfully' do
        mail_message = double('Mail::Message')
        expect(mail_message).to receive(:deliver_now)
        allow(NotificationMailer).to receive(:new_project_notification).and_return(mail_message)
        
        described_class.new.perform(
          mailer_class_name,
          mailer_method,
          delivery_method,
          delivery_args
        )
      end
      
      context 'with existing EmailDeliveryTracker' do
        let!(:tracker) do
          EmailDeliveryTracker.create!(
            project: project,
            user: user,
            email_type: 'new_project_notification',
            status: 'queued'
          )
        end
        
        it 'updates tracker to sent on successful delivery' do
          mail_message = double('Mail::Message')
          allow(mail_message).to receive(:deliver_now)
          allow(NotificationMailer).to receive(:new_project_notification).and_return(mail_message)
          
          described_class.new.perform(
            mailer_class_name,
            mailer_method,
            delivery_method,
            delivery_args
          )
          
          tracker.reload
          expect(tracker.status).to eq('sent')
          expect(tracker.sent_at).to be_present
        end
        
        it 'updates tracker to failed on Resend API error' do
          mail_message = double('Mail::Message')
          allow(mail_message).to receive(:deliver_now)
            .and_raise(Resend::Error.new('Invalid email address'))
          allow(NotificationMailer).to receive(:new_project_notification).and_return(mail_message)
          
          expect {
            described_class.new.perform(
              mailer_class_name,
              mailer_method,
              delivery_method,
              delivery_args
            )
          }.to raise_error(Resend::Error)
          
          tracker.reload
          expect(tracker.status).to eq('failed')
          expect(tracker.retry_count).to eq(1)
        end
        
        it 'retries on rate limit error' do
          mail_message = double('Mail::Message')
          allow(mail_message).to receive(:deliver_now)
            .and_raise(Resend::Error.new('rate_limit_exceeded'))
          allow(NotificationMailer).to receive(:new_project_notification).and_return(mail_message)
          
          expect {
            described_class.new.perform(
              mailer_class_name,
              mailer_method,
              delivery_method,
              delivery_args
            )
          }.to raise_error(RateLimitedMailDeliveryJob::ResendRateLimitError)
          
          # Tracker should remain queued during retries
          tracker.reload
          expect(tracker.status).to eq('queued')
        end
      end
      
      context 'without EmailDeliveryTracker' do
        it 'still delivers the email successfully' do
          mail_message = double('Mail::Message')
          expect(mail_message).to receive(:deliver_now)
          allow(NotificationMailer).to receive(:new_project_notification).and_return(mail_message)
          
          # Should not raise error even without tracker
          expect {
            described_class.new.perform(
              mailer_class_name,
              mailer_method,
              delivery_method,
              delivery_args
            )
          }.not_to raise_error
        end
      end
    end
    
    context 'when delivering user_approval_request_notification' do
      let(:mailer_method) { 'user_approval_request_notification' }
      let(:delivery_args) do
        {
          args: [user],
          kwargs: {},
          params: {}
        }
      end
      
      context 'with existing tracker' do
        let!(:tracker) do
          EmailDeliveryTracker.create!(
            project: project,  # Will be found without project for this email type
            user: user,
            email_type: 'user_approval_request',
            status: 'queued'
          )
        end
        
        it 'finds and updates tracker without project' do
          mail_message = double('Mail::Message')
          allow(mail_message).to receive(:deliver_now)
          allow(NotificationMailer).to receive(:user_approval_request_notification).and_return(mail_message)
          
          described_class.new.perform(
            mailer_class_name,
            mailer_method,
            delivery_method,
            delivery_args
          )
          
          tracker.reload
          expect(tracker.status).to eq('sent')
        end
      end
    end
    
    context 'when delivering unrecognized mailer method' do
      let(:mailer_method) { 'user_approval_notification' }  # This exists but isn't mapped for tracking
      let(:delivery_args) do
        {
          args: [user],
          kwargs: {},
          params: {}
        }
      end
      
      it 'delivers email without updating any tracker' do
        mail_message = double('Mail::Message')
        expect(mail_message).to receive(:deliver_now)
        allow(NotificationMailer).to receive(:user_approval_notification).and_return(mail_message)
        
        # Should not attempt to find/update tracker for unknown methods
        expect(EmailDeliveryTracker).not_to receive(:find_by)
        
        described_class.new.perform(
          mailer_class_name,
          mailer_method,
          delivery_method,
          delivery_args
        )
      end
    end
  end
  
  describe 'private #update_tracker_status' do
    let(:job) { described_class.new }
    
    context 'with valid tracker' do
      let!(:tracker) do
        EmailDeliveryTracker.create!(
          project: project,
          user: user,
          email_type: 'new_project_notification',
          status: 'queued'
        )
      end
      
      it 'marks tracker as sent' do
        job.send(:update_tracker_status, 
          'new_project_notification', 
          [project, user], 
          {}, 
          'sent'
        )
        
        tracker.reload
        expect(tracker.status).to eq('sent')
        expect(tracker.sent_at).to be_present
      end
      
      it 'marks tracker as failed with error message' do
        job.send(:update_tracker_status,
          'new_project_notification',
          [project, user],
          {},
          'failed',
          'Test error'
        )
        
        tracker.reload
        expect(tracker.status).to eq('failed')
        expect(tracker.retry_count).to eq(1)
      end
    end
    
    context 'when tracker update fails' do
      let!(:tracker) do
        EmailDeliveryTracker.create!(
          project: project,
          user: user,
          email_type: 'new_project_notification',
          status: 'queued'
        )
      end
      
      it 'logs error but does not raise' do
        allow(EmailDeliveryTracker).to receive(:find_by).and_raise(StandardError.new('DB error'))
        expect(Rails.logger).to receive(:error).with(/Failed to update email tracker/)
        
        # Should not raise error
        expect {
          job.send(:update_tracker_status,
            'new_project_notification',
            [project, user],
            {},
            'sent'
          )
        }.not_to raise_error
      end
    end
    
    context 'with different argument patterns' do
      it 'handles project and user args correctly' do
        tracker = EmailDeliveryTracker.create!(
          project: project,
          user: user,
          email_type: 'access_request',
          status: 'queued'
        )
        
        job.send(:update_tracker_status,
          'access_request_notification',
          [project, user],
          {},
          'sent'
        )
        
        tracker.reload
        expect(tracker.status).to eq('sent')
      end
      
      it 'handles user-only args correctly' do
        tracker = EmailDeliveryTracker.create!(
          project: project,
          user: user,
          email_type: 'user_approval_request',
          status: 'queued'
        )
        
        job.send(:update_tracker_status,
          'user_approval_request_notification',
          [user],
          {},
          'sent'
        )
        
        tracker.reload
        expect(tracker.status).to eq('sent')
      end
      
      it 'handles non-AR objects gracefully' do
        # Should not raise error with non-AR objects
        expect {
          job.send(:update_tracker_status,
            'new_project_notification',
            ['string', 123, nil],
            {},
            'sent'
          )
        }.not_to raise_error
      end
    end
    
    context 'email type mapping' do
      it 'maps all known mailer methods correctly' do
        mappings = {
          'new_project_notification' => 'new_project_notification',
          'admin_project_notification' => 'admin_project_notification',
          'user_approval_request_notification' => 'user_approval_request',
          'access_request_notification' => 'access_request',
          'approved_access_notification' => 'approved_access'
        }
        
        mappings.each do |method, expected_type|
          # Create tracker for each type
          tracker = EmailDeliveryTracker.create!(
            project: project,
            user: user,
            email_type: expected_type,
            status: 'queued'
          )
          
          job.send(:update_tracker_status,
            method,
            [project, user],
            {},
            'sent'
          )
          
          tracker.reload
          expect(tracker.status).to eq('sent'), 
            "Failed for #{method} -> #{expected_type}"
        end
      end
      
      it 'returns early for unknown mailer methods' do
        expect(EmailDeliveryTracker).not_to receive(:find_by)
        
        job.send(:update_tracker_status,
          'unknown_notification',
          [project, user],
          {},
          'sent'
        )
      end
    end
  end
  
  describe 'rate limiting configuration' do
    it 'has proper rate limiting configured' do
      # Check that the job has rate limiting configured
      expect(described_class.ancestors).to include(GoodJob::ActiveJobExtensions::Concurrency)
    end
  end
end