# ABOUTME: Test suite for BulkNotificationJob to verify rate limiting and idempotent delivery
# ABOUTME: Ensures emails are sent correctly without duplicates and respecting API limits

require 'rails_helper'

RSpec.describe BulkNotificationJob, type: :job do
  include ActiveJob::TestHelper
  
  let(:project) { create(:project) }
  let(:users) { create_list(:user, 5) }
  let(:user_ids) { users.map(&:id) }
  
  before do
    # Ensure users have profiles
    users.each do |user|
      create(:user_profile, user: user) unless user.user_profile
    end
  end
  
  describe '#perform' do
    it 'sends individual emails with rate limiting via deliver_later' do
      expect {
        BulkNotificationJob.perform_now(project, user_ids)
      }.to have_enqueued_mail(NotificationMailer, :new_project_notification).exactly(5).times
    end
    
    it 'creates EmailDeliveryTracker records for each user' do
      expect {
        BulkNotificationJob.perform_now(project, user_ids)
      }.to change(EmailDeliveryTracker, :count).by(5)
      
      users.each do |user|
        tracker = EmailDeliveryTracker.find_by(
          project: project,
          user: user,
          email_type: 'new_project_notification'
        )
        expect(tracker).to be_present
        expect(tracker.status).to eq('queued')
      end
    end
    
    it 'is idempotent - does not send duplicate emails' do
      # First run
      BulkNotificationJob.perform_now(project, user_ids)
      
      # Mark first user's email as sent
      tracker = EmailDeliveryTracker.find_by(
        project: project,
        user: users.first,
        email_type: 'new_project_notification'
      )
      tracker.mark_as_sent!
      
      # Clear enqueued jobs for test isolation
      clear_enqueued_jobs
      
      # Second run - should only queue 4 emails (skip the sent one)
      expect {
        BulkNotificationJob.perform_now(project, user_ids)
      }.to have_enqueued_mail(NotificationMailer, :new_project_notification).exactly(4).times
    end
    
    it 'handles errors gracefully without stopping the batch' do
      # Simulate an error for one user
      allow(EmailDeliveryTracker).to receive(:find_or_create_by)
        .with(project: project, user: users[2], email_type: 'new_project_notification')
        .and_raise(StandardError, 'Database error')
      
      # Should still process other users
      expect {
        BulkNotificationJob.perform_now(project, user_ids)
      }.to have_enqueued_mail(NotificationMailer, :new_project_notification).at_least(4).times
    end
    
    it 'logs when skipping already sent emails' do
      # Mark an email as sent
      EmailDeliveryTracker.create!(
        project: project,
        user: users.first,
        email_type: 'new_project_notification',
        status: 'sent',
        sent_at: 1.hour.ago
      )
      
      expect(Rails.logger).to receive(:info).with(/Skipping email for user #{users.first.id}/)
      
      BulkNotificationJob.perform_now(project, user_ids)
    end
  end
end