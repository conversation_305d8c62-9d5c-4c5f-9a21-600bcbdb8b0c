# ABOUTME: Comprehensive tests for WantPolicy that verify authorization patterns for all CRUD operations
# ABOUTME: Tests basic authentication requirements and owner-based permissions (no tier restrictions)

require 'rails_helper'

RSpec.describe WantPolicy, type: :policy do
  let(:user) { create(:user) }
  let(:other_user) { create(:user) }
  let(:want) { create(:want, user: user) }
  
  before do
    # Ensure all users have complete profiles to avoid redirection issues
    [user, other_user].each do |u|
      u.user_profile.update!(
        first_name: 'Test',
        last_name: 'User',
        city: 'Bratislava', 
        country: 'Slovakia'
      ) if u.user_profile
    end
  end

  describe 'basic access for authenticated users' do
    describe '#index?' do
      it 'allows all authenticated users to view wants index' do
        policy = described_class.new(want, user: user)
        expect(policy.apply(:index?)).to be true
      end
    end

    describe '#show?' do
      it 'allows all authenticated users to view individual wants' do
        policy = described_class.new(want, user: user)
        expect(policy.apply(:show?)).to be true
      end
    end

    describe '#show_my?' do
      it 'allows authenticated users to view their own wants' do
        policy = described_class.new(want, user: user)
        expect(policy.apply(:show_my?)).to be true
      end
    end

    describe '#create?' do
      it 'allows authenticated users to create wants' do
        policy = described_class.new(want, user: user)
        expect(policy.apply(:create?)).to be true
      end
    end
  end

  describe 'ownership rules' do
    let(:other_want) { create(:want, user: other_user) }
    
    before do
      other_user.user_profile.update!(
        first_name: 'Other',
        last_name: 'User',
        city: 'Bratislava',
        country: 'Slovakia'
      )
    end
    
    describe '#edit?' do
      it 'allows owner to edit their own want' do
        policy = described_class.new(want, user: user)
        expect(policy.apply(:edit?)).to be true
      end
      
      it 'denies non-owner from editing others wants' do
        policy = described_class.new(want, user: other_user)
        expect(policy.apply(:edit?)).to be false
      end
    end
    
    describe '#update?' do
      it 'allows owner to update their own want' do
        policy = described_class.new(want, user: user)
        expect(policy.apply(:update?)).to be true
      end
      
      it 'denies non-owner from updating others wants' do
        policy = described_class.new(want, user: other_user)
        expect(policy.apply(:update?)).to be false
      end
    end
    
    describe '#destroy?' do
      it 'allows owner to destroy their own want' do
        policy = described_class.new(want, user: user)
        expect(policy.apply(:destroy?)).to be true
      end
      
      it 'denies non-owner from destroying others wants' do
        policy = described_class.new(want, user: other_user)
        expect(policy.apply(:destroy?)).to be false
      end
    end
  end
end