# ABOUTME: Tests for Stripe webhook secret switching based on environment
# ABOUTME: Verifies that the correct webhook secret is selected for local vs production environments

require 'rails_helper'

RSpec.describe "Stripe webhook environment configuration" do
  let(:test_credentials) do
    {
      stripe: {
        test: {
          secret_key: 'sk_test_example',
          publishable_key: 'pk_test_example',
          webhook_secrets: {
            local: 'whsec_local_secret',
            production_test: 'whsec_production_test_secret'
          }
        }
      }
    }
  end

  before do
    allow(Rails.application.credentials).to receive(:dig).and_call_original
    allow(Rails.application.credentials).to receive(:dig).with(:stripe, :test).and_return(test_credentials[:stripe][:test])
  end

  context "in development environment with test mode" do
    before do
      allow(Rails).to receive(:env).and_return(ActiveSupport::StringInquirer.new('development'))
      stub_const('STRIPE_MODE', 'test')
      
      # Reload the initializer to apply our test configuration
      load Rails.root.join('config/initializers/stripe.rb')
    end

    it "uses the local webhook secret" do
      expect(STRIPE_WEBHOOK_SECRET).to eq('whsec_local_secret')
    end
  end

  context "in production environment with test mode" do
    before do
      allow(Rails).to receive(:env).and_return(ActiveSupport::StringInquirer.new('production'))
      stub_const('STRIPE_MODE', 'test')
      
      # Reload the initializer to apply our test configuration
      load Rails.root.join('config/initializers/stripe.rb')
    end

    it "uses the production test webhook secret" do
      expect(STRIPE_WEBHOOK_SECRET).to eq('whsec_production_test_secret')
    end
  end

  context "in production environment with live mode" do
    let(:live_credentials) do
      {
        secret_key: 'sk_live_example',
        publishable_key: 'pk_live_example',
        webhook_secrets: {
          local: 'whsec_live_local_secret',
          production_test: 'whsec_live_production_secret'
        }
      }
    end

    before do
      allow(Rails).to receive(:env).and_return(ActiveSupport::StringInquirer.new('production'))
      allow(Rails.application.credentials).to receive(:dig).with(:stripe, :live).and_return(live_credentials)
      
      # Need to stub the STRIPE_MODE before loading the initializer
      # First, remove the existing constant if it exists
      Object.send(:remove_const, :STRIPE_MODE) if defined?(STRIPE_MODE)
      ENV['STRIPE_MODE'] = 'live'
      
      # Mock Stripe account retrieval to prevent actual API call
      allow(Stripe::Account).to receive(:retrieve).and_return(double(id: 'acct_test'))
      
      # Reload the initializer to apply our test configuration
      load Rails.root.join('config/initializers/stripe.rb')
    end

    after do
      ENV['STRIPE_MODE'] = 'test' # Reset to test mode
    end

    it "uses the production webhook secret for live mode" do
      # In live mode, production should use production_test key 
      # (though name is confusing, it's production key for the environment)
      expect(STRIPE_WEBHOOK_SECRET).to eq('whsec_live_production_secret')
    end
  end
end