# ABOUTME: Factory for creating Subscription test records
# ABOUTME: Defines default attributes for user subscriptions in tests
FactoryBot.define do
  factory :subscription do
    association :user
    association :plan
    status { :active }
    payment_provider { :stripe }
    sequence(:stripe_subscription_id) { |n| "sub_test_#{n}" }
    sequence(:stripe_customer_id) { |n| "cus_test_#{n}" }
    stripe_status { 'active' }
    current_period_start { Time.current }
    current_period_end { 1.month.from_now }
    
    trait :trialing do
      status { :trialing }
      stripe_status { 'trialing' }
      trial_start { Time.current }
      trial_end { 14.days.from_now }
    end
    
    trait :past_due do
      status { :past_due }
      stripe_status { 'past_due' }
    end
    
    trait :canceled do
      status { :canceled }
      stripe_status { 'canceled' }
      canceled_at { Time.current }
      ended_at { Time.current }
    end
    
    trait :cancel_at_period_end do
      cancel_at_period_end { true }
      canceled_at { Time.current }
    end
    
    trait :unpaid do
      status { :unpaid }
      stripe_status { 'unpaid' }
    end
    
    trait :with_trial do
      trial_start { 14.days.ago }
      trial_end { Time.current }
    end
  end
end