# Preview all emails at http://localhost:3000/rails/mailers/notification_mailer
class NotificationMailerPreview < ActionMailer::Preview
  
  # Preview daily digest email at:
  # http://localhost:3000/rails/mailers/notification_mailer/daily_new_projects_digest
  def daily_new_projects_digest
    user = User.first || User.new(
      email: '<EMAIL>',
      user_profile: UserProfile.new(
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        default_language: 'en'
      )
    )
    
    # No count needed - generic message about new projects
    NotificationMailer.daily_new_projects_digest(user)
  end
  
end
