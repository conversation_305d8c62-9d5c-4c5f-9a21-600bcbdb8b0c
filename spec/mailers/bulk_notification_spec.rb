# ABOUTME: Test for the bulk notification system to ensure it prevents job queue flooding
# ABOUTME: Verifies that bulk notifications respect rate limits and create only one job

require 'rails_helper'

RSpec.describe 'Bulk Notification System', type: :job do
  include ActiveJob::TestHelper
  
  describe 'BulkNotificationJob' do
    let(:project) { create(:project, network_only: false, semi_public: true, project_status: true) }
    let(:users) { create_list(:user, 5) }
    let(:user_ids) { users.map(&:id) }

    before do
      # Clear deliveries before each test for clean slate
      ActionMailer::Base.deliveries.clear
      
      users.each do |user|
        create(:user_profile, user: user, email: user.email)
      end
    end

    it 'queues individual emails through deliver_later' do
      # The job now queues individual emails instead of sending immediately
      expect {
        BulkNotificationJob.perform_now(project, user_ids)
      }.to have_enqueued_mail(NotificationMailer, :new_project_notification).exactly(5).times
    end

    it 'uses rate-limited delivery job for each email' do
      # Each email should go through RateLimitedMailDeliveryJob
      perform_enqueued_jobs do
        BulkNotificationJob.perform_now(project, user_ids)
      end
      
      # Check that EmailDeliveryTracker records were created
      expect(EmailDeliveryTracker.count).to eq(5)
    end

    it 'creates EmailDeliveryTracker records' do
      expect {
        BulkNotificationJob.perform_now(project, user_ids)
      }.to change(EmailDeliveryTracker, :count).by(5)
    end

    it 'creates only one background job when using BulkNotificationJob' do
      # This should create only ONE BulkNotificationJob, not multiple
      expect {
        BulkNotificationJob.perform_later(project, user_ids)
      }.to have_enqueued_job(BulkNotificationJob).with(project, user_ids).once
    end

    context 'with many users' do
      let(:many_users) { create_list(:user, 100) }
      let(:many_user_ids) { many_users.map(&:id) }

      before do
        many_users.each do |user|
          create(:user_profile, user: user, email: user.email)
        end
      end

      it 'still creates only one job for 100 users' do
        expect {
          BulkNotificationJob.perform_later(project, many_user_ids)
        }.to have_enqueued_job(BulkNotificationJob).with(project, many_user_ids).once
      end
      
      it 'queues 100 individual mail delivery jobs when executed' do
        expect {
          BulkNotificationJob.perform_now(project, many_user_ids)
        }.to have_enqueued_mail(NotificationMailer, :new_project_notification).exactly(100).times
      end
    end
    
    context 'idempotency' do
      it 'does not send duplicate emails' do
        # First run
        BulkNotificationJob.perform_now(project, user_ids)
        
        # Mark one as sent
        EmailDeliveryTracker.first.mark_as_sent!
        
        # Clear enqueued jobs for test isolation
        clear_enqueued_jobs
        
        # Second run should only queue 4 emails
        expect {
          BulkNotificationJob.perform_now(project, user_ids)
        }.to have_enqueued_mail(NotificationMailer, :new_project_notification).exactly(4).times
      end
    end
  end
end