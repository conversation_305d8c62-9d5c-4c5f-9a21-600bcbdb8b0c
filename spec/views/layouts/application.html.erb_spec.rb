# ABOUTME: Tests for application layout view, especially sidebar menu visibility
# ABOUTME: Ensures subscription link is only visible to pilot tier users
require 'rails_helper'

RSpec.describe "layouts/application", type: :view do
  let(:user) { create(:user) }
  let(:user_profile) { user.user_profile }
  
  before do
    allow(view).to receive(:user_signed_in?).and_return(true)
    allow(view).to receive(:current_user).and_return(user)
    allow(view).to receive(:current_page?).and_return(false)
    allow(view).to receive(:allowed_to?).and_return(true)
    
    # Stub all the paths used in the layout
    allow(view).to receive(:edit_user_profile_path).and_return('/profile/edit')
    allow(view).to receive(:subscriptions_path).and_return('/subscriptions')
    allow(view).to receive(:projects_path).and_return('/projects')
    allow(view).to receive(:show_my_projects_path).and_return('/my_projects')
    allow(view).to receive(:wants_path).and_return('/wants')
    allow(view).to receive(:show_my_wants_path).and_return('/my_wants')
    allow(view).to receive(:network_connections_path).and_return('/network')
    allow(view).to receive(:my_network_connections_path).and_return('/my_network')
    allow(view).to receive(:invite_network_connections_path).and_return('/invite')
    allow(view).to receive(:connection_requests_path).and_return('/requests')
    allow(view).to receive(:user_admin_dashboard_path).and_return('/admin/dashboard')
    allow(view).to receive(:admin_subscriptions_path).and_return('/admin/subscriptions')
    allow(view).to receive(:admin_referral_codes_path).and_return('/admin/referral_codes')
    allow(view).to receive(:set_user_language_path).and_return('/set_language')
    allow(view).to receive(:destroy_user_session_path).and_return('/logout')
    allow(view).to receive(:root_path).and_return('/')
    
    assign(:current_user, user)
  end

  describe "subscription link visibility" do
    context "when user is in pilot tier" do
      before do
        user.update!(subscription_tier: :pilot)
      end

      it "shows the subscription link" do
        render
        expect(rendered).to have_link("Subscriptions", href: '/subscriptions')
      end
    end

    context "when user is in premium tier" do
      before do
        user.update!(subscription_tier: :premium)
      end

      it "does not show the subscription link" do
        render
        expect(rendered).not_to have_link("Subscriptions", href: '/subscriptions')
      end
    end

    context "when user is in free tier" do
      before do
        user.update!(subscription_tier: :free)
      end

      it "does not show the subscription link" do
        render
        expect(rendered).not_to have_link("Subscriptions", href: '/subscriptions')
      end
    end

    context "when user is admin" do
      before do
        user.update!(role: :super_boss, subscription_tier: :free)
      end

      it "shows the subscription link even if not in pilot tier" do
        render
        expect(rendered).to have_link("Subscriptions", href: '/subscriptions')
      end
    end
  end
end