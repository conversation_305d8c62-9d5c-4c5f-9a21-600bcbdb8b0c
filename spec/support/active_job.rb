# ABOUTME: Configuration for ActiveJob testing with test adapter
# ABOUTME: Ensures job tests can properly enqueue and verify job execution

RSpec.configure do |config|
  config.include ActiveJob::TestHelper, type: :job
  
  config.before(:each, type: :job) do
    # Use test adapter for job specs
    ActiveJob::Base.queue_adapter = :test
  end
  
  config.after(:each, type: :job) do
    # Clean up enqueued jobs after each test
    clear_enqueued_jobs if respond_to?(:clear_enqueued_jobs)
  end
end