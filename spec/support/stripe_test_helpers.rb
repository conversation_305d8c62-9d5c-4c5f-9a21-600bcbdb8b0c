# ABOUTME: Provides secure Stripe test helpers with proper signature generation
# ABOUTME: Ensures webhook tests use real signature verification, not mocked security

module StripeTestHelpers
  # Generate a real Stripe webhook signature using <PERSON><PERSON>'s official method
  # This ensures we test the actual signature verification logic
  def generate_stripe_signature(payload, secret = test_webhook_secret, timestamp = nil)
    timestamp ||= Time.current
    
    # Use Stripe's official signature computation method
    signature = Stripe::Webhook::Signature.compute_signature(
      timestamp,
      payload.is_a?(String) ? payload : payload.to_json,
      secret
    )
    
    # Return in <PERSON><PERSON>'s expected format
    "t=#{timestamp.to_i},v1=#{signature}"
  end
  
  # Generate an expired signature (older than 5 minutes)
  def generate_expired_stripe_signature(payload, secret = test_webhook_secret)
    expired_timestamp = 6.minutes.ago
    generate_stripe_signature(payload, secret, expired_timestamp)
  end
  
  # Generate signature with wrong secret
  def generate_invalid_stripe_signature(payload)
    generate_stripe_signature(payload, 'wrong_secret')
  end
  
  # Default test webhook secret
  def test_webhook_secret
    'whsec_test_secret_123'
  end
  
  # Valid Stripe webhook event structure
  def valid_stripe_event(type: 'customer.subscription.created', object: nil)
    {
      id: "evt_test_#{SecureRandom.hex(8)}",
      object: 'event',
      api_version: '2023-10-16',
      created: Time.current.to_i,
      data: {
        object: object || subscription_object
      },
      livemode: false,
      pending_webhooks: 1,
      request: {
        id: "req_test_#{SecureRandom.hex(8)}",
        idempotency_key: nil
      },
      type: type
    }
  end
  
  # Valid subscription object
  def subscription_object
    {
      id: "sub_test_#{SecureRandom.hex(8)}",
      object: 'subscription',
      application: nil,
      application_fee_percent: nil,
      automatic_tax: {
        enabled: false
      },
      billing_cycle_anchor: Time.current.to_i,
      billing_thresholds: nil,
      cancel_at: nil,
      cancel_at_period_end: false,
      canceled_at: nil,
      cancellation_details: {
        comment: nil,
        feedback: nil,
        reason: nil
      },
      collection_method: 'charge_automatically',
      created: Time.current.to_i,
      currency: 'eur',
      current_period_end: 1.month.from_now.to_i,
      current_period_start: Time.current.to_i,
      customer: "cus_test_#{SecureRandom.hex(8)}",
      days_until_due: nil,
      default_payment_method: nil,
      default_source: nil,
      default_tax_rates: [],
      description: nil,
      discount: nil,
      ended_at: nil,
      items: {
        object: 'list',
        data: [{
          id: "si_test_#{SecureRandom.hex(8)}",
          object: 'subscription_item',
          created: Time.current.to_i,
          metadata: {},
          price: {
            id: "price_test_#{SecureRandom.hex(8)}",
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: Time.current.to_i,
            currency: 'eur',
            livemode: false,
            lookup_key: nil,
            metadata: {},
            nickname: nil,
            product: "prod_test_#{SecureRandom.hex(8)}",
            recurring: {
              aggregate_usage: nil,
              interval: 'month',
              interval_count: 1,
              trial_period_days: nil,
              usage_type: 'licensed'
            },
            tax_behavior: 'unspecified',
            tiers_mode: nil,
            transform_quantity: nil,
            type: 'recurring',
            unit_amount: 1999,
            unit_amount_decimal: '1999'
          },
          quantity: 1,
          subscription: 'sub_test_123',
          tax_rates: []
        }],
        has_more: false,
        total_count: 1,
        url: '/v1/subscription_items?subscription=sub_test_123'
      },
      latest_invoice: "inv_test_#{SecureRandom.hex(8)}",
      livemode: false,
      metadata: {},
      next_pending_invoice_item_invoice: nil,
      on_behalf_of: nil,
      pause_collection: nil,
      payment_settings: {
        payment_method_options: nil,
        payment_method_types: nil,
        save_default_payment_method: 'off'
      },
      pending_invoice_item_interval: nil,
      pending_setup_intent: nil,
      pending_update: nil,
      plan: nil,
      quantity: 1,
      schedule: nil,
      start_date: Time.current.to_i,
      status: 'active',
      test_clock: nil,
      transfer_data: nil,
      trial_end: nil,
      trial_settings: {
        end_behavior: {
          missing_payment_method: 'create_invoice'
        }
      },
      trial_start: nil
    }
  end
  
  # Valid checkout session completed object
  def checkout_session_object
    {
      id: "cs_test_#{SecureRandom.hex(8)}",
      object: 'checkout.session',
      after_expiration: nil,
      allow_promotion_codes: false,
      amount_subtotal: 1999,
      amount_total: 1999,
      automatic_tax: {
        enabled: false,
        status: nil
      },
      billing_address_collection: nil,
      cancel_url: 'https://example.com/cancel',
      client_reference_id: nil,
      consent: nil,
      consent_collection: nil,
      created: Time.current.to_i,
      currency: 'eur',
      currency_conversion: nil,
      custom_fields: [],
      custom_text: {
        shipping_address: nil,
        submit: nil,
        terms_of_service_acceptance: nil
      },
      customer: "cus_test_#{SecureRandom.hex(8)}",
      customer_creation: 'if_required',
      customer_details: {
        address: nil,
        email: '<EMAIL>',
        name: 'Test User',
        phone: nil,
        tax_exempt: 'none',
        tax_ids: []
      },
      customer_email: '<EMAIL>',
      expires_at: 24.hours.from_now.to_i,
      invoice: nil,
      invoice_creation: nil,
      line_items: {
        object: 'list',
        data: [{
          id: "li_test_#{SecureRandom.hex(8)}",
          object: 'item',
          amount_subtotal: 1999,
          amount_total: 1999,
          currency: 'eur',
          description: 'Pro Plan',
          price: {
            id: "price_test_#{SecureRandom.hex(8)}",
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: Time.current.to_i,
            currency: 'eur',
            livemode: false,
            lookup_key: nil,
            metadata: {},
            nickname: nil,
            product: "prod_test_#{SecureRandom.hex(8)}",
            recurring: {
              aggregate_usage: nil,
              interval: 'month',
              interval_count: 1,
              trial_period_days: nil,
              usage_type: 'licensed'
            },
            tax_behavior: 'unspecified',
            tiers_mode: nil,
            transform_quantity: nil,
            type: 'recurring',
            unit_amount: 1999,
            unit_amount_decimal: '1999'
          },
          quantity: 1
        }],
        has_more: false,
        url: '/v1/checkout/sessions/cs_test_123/line_items'
      },
      livemode: false,
      locale: nil,
      metadata: {},
      mode: 'subscription',
      payment_intent: nil,
      payment_link: nil,
      payment_method_collection: 'always',
      payment_method_options: {},
      payment_method_types: ['card'],
      payment_status: 'paid',
      phone_number_collection: {
        enabled: false
      },
      recovered_from: nil,
      setup_intent: nil,
      shipping_address_collection: nil,
      shipping_cost: nil,
      shipping_details: nil,
      shipping_options: [],
      status: 'complete',
      submit_type: nil,
      subscription: "sub_test_#{SecureRandom.hex(8)}",
      success_url: 'https://example.com/success?session_id={CHECKOUT_SESSION_ID}',
      total_details: {
        amount_discount: 0,
        amount_shipping: 0,
        amount_tax: 0
      },
      url: nil
    }
  end
  
  # Valid invoice payment succeeded object
  def invoice_object
    {
      id: "in_test_#{SecureRandom.hex(8)}",
      object: 'invoice',
      account_country: 'US',
      account_name: 'Test Company',
      account_tax_ids: nil,
      amount_due: 1999,
      amount_paid: 1999,
      amount_remaining: 0,
      amount_shipping: 0,
      application: nil,
      application_fee_amount: nil,
      attempt_count: 1,
      attempted: true,
      auto_advance: true,
      automatic_tax: {
        enabled: false,
        status: nil
      },
      billing_reason: 'subscription_cycle',
      charge: "ch_test_#{SecureRandom.hex(8)}",
      collection_method: 'charge_automatically',
      created: Time.current.to_i,
      currency: 'eur',
      custom_fields: nil,
      customer: "cus_test_#{SecureRandom.hex(8)}",
      customer_address: nil,
      customer_email: '<EMAIL>',
      customer_name: 'Test User',
      customer_phone: nil,
      customer_shipping: nil,
      customer_tax_exempt: 'none',
      customer_tax_ids: [],
      default_payment_method: nil,
      default_source: nil,
      default_tax_rates: [],
      description: nil,
      discount: nil,
      discounts: [],
      due_date: nil,
      ending_balance: 0,
      footer: nil,
      from_invoice: nil,
      hosted_invoice_url: 'https://invoice.stripe.com/i/test_123',
      invoice_pdf: 'https://invoice.stripe.com/i/test_123/pdf',
      last_finalization_error: nil,
      latest_revision: nil,
      lines: {
        object: 'list',
        data: [{
          id: "il_test_#{SecureRandom.hex(8)}",
          object: 'line_item',
          amount: 1999,
          currency: 'eur',
          description: 'Pro Plan (Monthly)',
          discount_amounts: [],
          discountable: true,
          discounts: [],
          livemode: false,
          metadata: {},
          period: {
            end: 1.month.from_now.to_i,
            start: Time.current.to_i
          },
          plan: nil,
          price: {
            id: "price_test_#{SecureRandom.hex(8)}",
            object: 'price',
            active: true,
            billing_scheme: 'per_unit',
            created: Time.current.to_i,
            currency: 'eur',
            livemode: false,
            lookup_key: nil,
            metadata: {},
            nickname: nil,
            product: "prod_test_#{SecureRandom.hex(8)}",
            recurring: {
              aggregate_usage: nil,
              interval: 'month',
              interval_count: 1,
              trial_period_days: nil,
              usage_type: 'licensed'
            },
            tax_behavior: 'unspecified',
            tiers_mode: nil,
            transform_quantity: nil,
            type: 'recurring',
            unit_amount: 1999,
            unit_amount_decimal: '1999'
          },
          proration: false,
          proration_details: {
            credited_items: nil
          },
          quantity: 1,
          subscription: "sub_test_#{SecureRandom.hex(8)}",
          subscription_item: "si_test_#{SecureRandom.hex(8)}",
          tax_amounts: [],
          tax_rates: [],
          type: 'subscription'
        }],
        has_more: false,
        url: '/v1/invoices/in_test_123/lines'
      },
      livemode: false,
      metadata: {},
      next_payment_attempt: nil,
      number: 'TEST-0001',
      on_behalf_of: nil,
      paid: true,
      paid_out_of_band: false,
      payment_intent: "pi_test_#{SecureRandom.hex(8)}",
      payment_settings: {
        default_mandate: nil,
        payment_method_options: nil,
        payment_method_types: nil
      },
      period_end: Time.current.to_i,
      period_start: 1.month.ago.to_i,
      post_payment_credit_notes_amount: 0,
      pre_payment_credit_notes_amount: 0,
      quote: nil,
      receipt_number: nil,
      rendering_options: nil,
      starting_balance: 0,
      statement_descriptor: nil,
      status: 'paid',
      status_transitions: {
        finalized_at: Time.current.to_i,
        marked_uncollectible_at: nil,
        paid_at: Time.current.to_i,
        voided_at: nil
      },
      subscription: "sub_test_#{SecureRandom.hex(8)}",
      subtotal: 1999,
      subtotal_excluding_tax: 1999,
      tax: nil,
      test_clock: nil,
      total: 1999,
      total_discount_amounts: [],
      total_excluding_tax: 1999,
      total_tax_amounts: [],
      transfer_data: nil,
      webhooks_delivered_at: Time.current.to_i
    }
  end
end

# Include in RSpec tests
RSpec.configure do |config|
  config.include StripeTestHelpers
end