# ABOUTME: Test suite for StripeSubscriptionService functionality 
# ABOUTME: Validates Stripe checkout session and billing portal locale configuration
require 'rails_helper'

RSpec.describe StripeSubscriptionService do
  let(:user) { create(:user, stripe_customer_id: 'cus_test123') }
  let(:service) { described_class.new(user) }
  
  describe '#create_checkout_session' do
    let(:price_id) { 'price_123' }
    let(:success_url) { 'https://example.com/success' }
    let(:cancel_url) { 'https://example.com/cancel' }
    
    before do
      # Mock Stripe API calls
      allow(Stripe::Price).to receive(:retrieve).with(price_id).and_return(
        double('Price', product: 'prod_123')
      )
      allow(Stripe::Product).to receive(:retrieve).with('prod_123').and_return(
        double('Product')
      )
    end
    
    context 'with Slovak locale' do
      before { I18n.locale = :sk }
      
      it 'creates checkout session with Slovak locale' do
        expect(Stripe::Checkout::Session).to receive(:create).with(
          hash_including(locale: 'sk')
        ).and_return(double('Session', id: 'sess_123'))
        
        service.create_checkout_session(price_id, success_url, cancel_url)
      end
    end
    
    context 'with English locale' do
      before { I18n.locale = :en }
      
      it 'creates checkout session with English locale' do
        expect(Stripe::Checkout::Session).to receive(:create).with(
          hash_including(locale: 'en')
        ).and_return(double('Session', id: 'sess_123'))
        
        service.create_checkout_session(price_id, success_url, cancel_url)
      end
    end
    
    it 'includes all required parameters along with locale' do
      I18n.locale = :sk
      
      expect(Stripe::Checkout::Session).to receive(:create).with(
        hash_including(
          customer: 'cus_test123',
          payment_method_types: ['card'],
          line_items: [{
            price: price_id,
            quantity: 1
          }],
          locale: 'sk',
          mode: 'subscription',
          success_url: success_url,
          cancel_url: cancel_url,
          client_reference_id: user.id.to_s
        )
      ).and_return(double('Session', id: 'sess_123'))
      
      service.create_checkout_session(price_id, success_url, cancel_url)
    end
  end
  
  describe '#create_portal_session' do
    let(:return_url) { 'https://example.com/return' }
    let(:configuration) { double('Configuration', id: 'config_123') }
    
    before do
      allow(service).to receive(:ensure_portal_configuration!).and_return(configuration)
    end
    
    context 'with Slovak locale' do
      before { I18n.locale = :sk }
      
      it 'creates billing portal session with Slovak locale' do
        expect(Stripe::BillingPortal::Session).to receive(:create).with(
          hash_including(
            customer: 'cus_test123',
            return_url: return_url,
            locale: 'sk',
            configuration: 'config_123'
          )
        ).and_return(double('PortalSession', id: 'portal_123'))
        
        service.create_portal_session(return_url)
      end
    end
    
    context 'with English locale' do
      before { I18n.locale = :en }
      
      it 'creates billing portal session with English locale' do
        expect(Stripe::BillingPortal::Session).to receive(:create).with(
          hash_including(
            customer: 'cus_test123',
            return_url: return_url,
            locale: 'en',
            configuration: 'config_123'
          )
        ).and_return(double('PortalSession', id: 'portal_123'))
        
        service.create_portal_session(return_url)
      end
    end
  end
  
  describe 'START coupon handling for localization' do
    let(:price_id) { 'price_yearly' }
    let(:success_url) { 'https://example.com/success' }
    let(:cancel_url) { 'https://example.com/cancel' }
    
    before do
      allow(Stripe::Price).to receive(:retrieve).and_return(double('Price', product: 'prod_123', unit_amount: 19000))
      allow(Stripe::Product).to receive(:retrieve).and_return(double('Product'))
      I18n.locale = :sk
    end
    
    context 'with 100% discount START coupon' do
      before do
        allow(service).to receive(:find_coupon_id).with('START').and_return('coupon_start_100')
        allow(service).to receive(:is_full_discount_coupon?).with('coupon_start_100').and_return(true)
      end
      
      it 'uses one-time payment mode instead of subscription to avoid untranslated promotional text' do
        expect(Stripe::Checkout::Session).to receive(:create).with(
          hash_including(
            locale: 'sk',
            mode: 'payment',
            discounts: [{ coupon: 'coupon_start_100' }],
            custom_text: {
              after_submit: {
                message: "Toto je jednorazová platba a po skončení ročnej licencie nebudú účtované žiadne ďalšie poplatky."
              }
            }
          )
        ).and_return(double('Session', id: 'sess_123'))
        
        service.create_checkout_session(price_id, success_url, cancel_url, coupon_code: 'START')
      end
      
      it 'uses English message when locale is English' do
        I18n.locale = :en
        
        expect(Stripe::Checkout::Session).to receive(:create).with(
          hash_including(
            locale: 'en',
            mode: 'payment',
            custom_text: {
              after_submit: {
                message: "This is one time payment and after annual license finish there will be no further payment charged."
              }
            }
          )
        ).and_return(double('Session', id: 'sess_123'))
        
        service.create_checkout_session(price_id, success_url, cancel_url, coupon_code: 'START')
      end
    end
    
    context 'with partial discount START coupon' do
      before do
        allow(service).to receive(:find_coupon_id).with('START').and_return('coupon_start_50')
        allow(service).to receive(:is_full_discount_coupon?).with('coupon_start_50').and_return(false)
      end
      
      it 'uses regular subscription mode with discount for partial discount coupons' do
        expect(Stripe::Checkout::Session).to receive(:create).with(
          hash_including(
            locale: 'sk',
            mode: 'subscription',
            discounts: [{ coupon: 'coupon_start_50' }]
          )
        ).and_return(double('Session', id: 'sess_123'))
        
        service.create_checkout_session(price_id, success_url, cancel_url, coupon_code: 'START')
      end
    end
    
    it 'uses regular discount for other coupons' do
      allow(service).to receive(:find_coupon_id).with('OTHER_COUPON').and_return('coupon_123')
      
      expect(Stripe::Checkout::Session).to receive(:create).with(
        hash_including(
          locale: 'sk',
          discounts: [{ coupon: 'coupon_123' }]
        )
      ).and_return(double('Session', id: 'sess_123'))
      
      service.create_checkout_session(price_id, success_url, cancel_url, coupon_code: 'OTHER_COUPON')
    end
  end
  
  describe '#is_full_discount_coupon?' do
    it 'returns true for 100% discount coupon' do
      coupon = double('Coupon', percent_off: 100)
      allow(Stripe::Coupon).to receive(:retrieve).with('coupon_100').and_return(coupon)
      
      expect(service.send(:is_full_discount_coupon?, 'coupon_100')).to be true
    end
    
    it 'returns false for partial discount coupon' do
      coupon = double('Coupon', percent_off: 50)
      allow(Stripe::Coupon).to receive(:retrieve).with('coupon_50').and_return(coupon)
      
      expect(service.send(:is_full_discount_coupon?, 'coupon_50')).to be false
    end
    
    it 'returns false for invalid coupon' do
      allow(Stripe::Coupon).to receive(:retrieve).with('invalid_coupon').and_raise(Stripe::InvalidRequestError.new('Not found', nil))
      
      expect(service.send(:is_full_discount_coupon?, 'invalid_coupon')).to be false
    end
    
    it 'returns false for nil coupon' do
      expect(service.send(:is_full_discount_coupon?, nil)).to be false
    end
  end
  
  describe 'locale consistency with user preferences' do
    before do
      create(:user_profile, user: user, default_language: 'sk')
      user.reload
      # Set locale explicitly to 'sk' to simulate ApplicationController behavior
      I18n.locale = 'sk'
    end
    
    it 'uses user profile language preference for checkout session' do
      allow(Stripe::Price).to receive(:retrieve).and_return(double('Price', product: 'prod_123'))
      allow(Stripe::Product).to receive(:retrieve).and_return(double('Product'))
      
      expect(Stripe::Checkout::Session).to receive(:create).with(
        hash_including(locale: 'sk')
      ).and_return(double('Session', id: 'sess_123'))
      
      service.create_checkout_session('price_123', 'https://example.com/success', 'https://example.com/cancel')
    end
    
    it 'uses user profile language preference for billing portal' do
      configuration = double('Configuration', id: 'config_123')
      allow(service).to receive(:ensure_portal_configuration!).and_return(configuration)
      
      expect(Stripe::BillingPortal::Session).to receive(:create).with(
        hash_including(locale: 'sk')
      ).and_return(double('PortalSession', id: 'portal_123'))
      
      service.create_portal_session('https://example.com/return')
    end
  end
end