#!/usr/bin/env bash
# exit on error
set -o errexit

# gem update --system
# Pin RubyGems to a compatible version to fix build errors
gem update --system 3.4.22

# Install Ruby dependencies
bundle install

# Install JavaScript dependencies and build assets
npm install
npm run build

# Precompile assets
bundle exec rails assets:precompile

# Run database migrations
bundle exec rails db:migrate

# Clean up
bundle exec rails assets:clean