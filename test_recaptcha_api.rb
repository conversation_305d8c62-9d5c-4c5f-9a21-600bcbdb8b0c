#!/usr/bin/env ruby
# ABOUTME: Direct test of reCAPTCHA API with Rails credentials
# ABOUTME: Helps diagnose why verification is failing

require 'net/http'
require 'json'
require 'uri'

# Load Rails environment to access credentials
require_relative 'config/environment'

# Test with a dummy token to see what error we get
def test_recaptcha_api
  secret_key = Rails.application.credentials.recaptcha[:secret_key]
  
  if secret_key.blank?
    puts "ERROR: reCAPTCHA secret key not found in Rails credentials"
    return
  end
  
  # Test 1: Invalid token (expected to fail but show error type)
  puts "=== TEST 1: Testing with invalid token ==="
  test_token = "03AGdBq26gJ" + "x" * 100  # Fake token
  
  uri = URI('https://www.google.com/recaptcha/api/siteverify')
  response = Net::HTTP.post_form(uri, {
    'secret' => secret_key,
    'response' => test_token
  })
  
  result = JSON.parse(response.body)
  puts "Response: #{result.inspect}"
  
  if result['error-codes']
    puts "\nError codes explanation:"
    result['error-codes'].each do |code|
      case code
      when 'missing-input-secret'
        puts "  • #{code}: The secret parameter is missing"
      when 'invalid-input-secret'
        puts "  • #{code}: The secret parameter is invalid or malformed"
      when 'missing-input-response'
        puts "  • #{code}: The response parameter is missing"
      when 'invalid-input-response'
        puts "  • #{code}: The response parameter is invalid or malformed"
      when 'bad-request'
        puts "  • #{code}: The request is invalid or malformed"
      when 'timeout-or-duplicate'
        puts "  • #{code}: The response is no longer valid: either is too old or has been used previously"
      else
        puts "  • #{code}: Unknown error"
      end
    end
  end
  
  puts "\n=== COMMON ISSUES ==="
  puts "1. Domain not registered: Check https://www.google.com/recaptcha/admin"
  puts "   - Your site key: 6LdE8aYrAAAAADuskrEwdLvKwcVPth_rKaRtvYt8"
  puts "   - Must include: app.unlisters.com"
  puts ""
  puts "2. Wrong reCAPTCHA version:"
  puts "   - If using v2: Won't have 'score' in response"
  puts "   - If using v3: Will have 'score' between 0.0 and 1.0"
  puts ""
  puts "3. Score threshold too high:"
  puts "   - Current code requires score >= 0.5"
  puts "   - Legitimate users might score lower"
  puts ""
  puts "4. Production vs Development keys:"
  puts "   - Make sure using production keys on production"
  
rescue => e
  puts "Error: #{e.message}"
  puts e.backtrace.first(3)
end

test_recaptcha_api