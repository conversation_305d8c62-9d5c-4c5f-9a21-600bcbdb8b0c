# 2025-09-10 Ruby Version Upgrade Analysis

## Current State

### Ruby & RubyGems Versions
- **Ruby**: 3.1.2 (EOL as of 2025-03-31)
- **RubyGems**: 3.6.3 locally, pinned to 3.4.22 in render-build.sh
- **Rails**: 7.0.8 (requires Ruby 2.7.0+)

### Nokogiri Dependency Issue
**Current**: nokogiri 1.18.1 
**Latest**: nokogiri 1.18.9
**Problem**: Nokogiri is not directly used but required by Rails core components:
- actiontext → nokogiri
- capybara → xpath → nokogiri  
- rails-html-sanitizer → loofah → nokogiri

## Ruby Version Compatibility Matrix

| Rails Version | Minimum Ruby | Recommended Ruby |
|--------------|--------------|------------------|
| Rails 7.0    | Ruby 2.7.0   | Ruby 3.0+       |
| Rails 7.1    | Ruby 2.7.0   | Ruby 3.1+       |
| Rails 7.2    | Ruby 3.1.0   | Ruby 3.2+       |
| Rails 8.0    | Ruby 3.2.0   | Ruby 3.3+       |

## Upgrade Recommendations

### Option 1: Conservative Upgrade to Ruby 3.2.6 (RECOMMENDED)
**Risk Level**: LOW
**Benefits**:
- Officially supported by Rails 7.0.8
- Nokogiri 1.18.1+ works perfectly
- Security updates until 2026-03-31
- No RubyGems version conflicts
- Minimal gem updates required

**Required Changes**:
1. Update `.ruby-version` to `3.2.6`
2. Update `Gemfile` ruby version to `"3.2.6"`
3. Remove RubyGems pin from `render-build.sh`
4. Test thoroughly

### Option 2: Future-Proof Upgrade to Ruby 3.3.6
**Risk Level**: MEDIUM
**Benefits**:
- Latest stable Ruby with all performance improvements
- Longest support timeline (until 2027-03-31)
- Ready for Rails 8.0 when needed
- Best performance and memory usage

**Required Changes**:
1. Update `.ruby-version` to `3.3.6`
2. Update `Gemfile` ruby version to `"3.3.6"`
3. Remove RubyGems pin from `render-build.sh`
4. May need to update some gems:
   - Potential deprecation warnings to address
   - Some gems may need minor updates

### Option 3: Stay on Ruby 3.1.2 with Workarounds
**Risk Level**: HIGH (Long-term)
**Issues**:
- Ruby 3.1.2 reaches EOL March 2025
- No security updates after EOL
- RubyGems version conflicts will worsen
- Technical debt accumulation

## Gem Compatibility Assessment

### Gems Likely Needing Updates for Ruby 3.2+
- **bootsnap**: May need update for Ruby 3.2+ optimizations
- **debug**: Should be updated for better Ruby 3.2+ support
- **sassc-rails**: Known issues with newer Ruby, may need replacement with `dartsass-rails`

### Gems Working Fine
- All core Rails gems (7.0.8+)
- devise, devise_invitable
- aws-sdk-s3
- good_job
- All testing gems (rspec, capybara)

## Migration Steps for Ruby 3.2.6

1. **Local Testing**:
   ```bash
   # Install Ruby 3.2.6
   mise use ruby@3.2.6
   
   # Update Gemfile
   # Change: ruby "3.1.2" → ruby "3.2.6"
   
   # Update gems
   bundle update --ruby
   bundle install
   
   # Run tests
   bundle exec rspec
   ```

2. **Update render-build.sh**:
   ```bash
   #!/usr/bin/env bash
   set -o errexit
   
   # Remove RubyGems pin - Ruby 3.2.6 includes compatible version
   # gem update --system 3.4.22  ← REMOVE THIS LINE
   
   bundle install
   npm install
   npm run build
   bundle exec rails assets:precompile
   bundle exec rails db:migrate
   bundle exec rails assets:clean
   ```

3. **Update Deployment Config**:
   - Update Render.com Ruby version setting
   - Clear build cache for fresh install

## Risk Mitigation

1. **Before Upgrade**:
   - Full test suite pass on current version
   - Database backup
   - Document current gem versions

2. **During Upgrade**:
   - Test in staging environment first
   - Monitor memory usage (Ruby 3.2+ has better GC)
   - Check for deprecation warnings

3. **After Upgrade**:
   - Run full test suite
   - Performance testing
   - Monitor application logs for 24-48 hours

## Conclusion

**Recommendation**: Upgrade to Ruby 3.2.6 now. This provides:
- Immediate resolution of nokogiri/RubyGems conflicts
- 15+ months of security support remaining
- Stable, well-tested Ruby version
- Minimal code changes required
- Clear upgrade path to Ruby 3.3+ later if needed

The upgrade risk is low because Rails 7.0.8 fully supports Ruby 3.2, and most gems in the Gemfile are actively maintained with Ruby 3.2 support already available.