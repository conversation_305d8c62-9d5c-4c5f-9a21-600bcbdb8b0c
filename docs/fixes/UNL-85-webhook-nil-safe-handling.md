# UNL-85: Webhook TypeError Fix and Subscription Views

## Problem

### Critical Bug: Webhook Processing Failure
- **Error**: `TypeError: can't convert NilClass into an exact number` at line 59 of StripeWebhookJob
- **Root Cause**: Stripe webhook payload had `nil` values for `current_period_start` and `current_period_end`, causing `Time.at(nil)` to crash
- **Impact**: 
  - Webhook fails and rolls back
  - No subscription record created
  - User sees "no subscription" despite paying
  - After 5 retries, job permanently fails

### Missing Views
- Only `index.html.erb` existed for subscriptions
- Missing `show.html.erb` and `new.html.erb` views
- No way for users to view subscription details or select plans

## Solution

### 1. Webhook Resilience (Critical Fix)
Added nil-safe handling to StripeWebhookJob for all timestamp fields:
- `handle_subscription_created`: Uses `Time.current` and `1.month.from_now` as defaults when timestamps are nil
- `handle_subscription_updated`: Keeps existing timestamps when update has nil values
- `handle_subscription_canceled`: Uses `Time.current` when `canceled_at` is nil

### 2. Immediate Subscription Creation on Success
Modified `SubscriptionsController#success` to:
- Accept `session_id` parameter from Stripe redirect
- Fetch subscription from Stripe API immediately after checkout
- Create local subscription record without waiting for webhook
- Fall back to webhook processing if immediate creation fails

### 3. Created Missing Views

#### show.html.erb
- Displays current subscription details
- Shows plan name, price, next billing date
- Cancel/reactivate buttons (already work in controller)
- Link to Stripe billing portal
- Recent invoices list
- Upcoming invoice preview

#### new.html.erb
- Plan selection with monthly/annual toggle
- Coupon code input field with validation
- Dynamic price display from Stripe
- Checkout button for each plan
- Reuses components from index page for consistency

## Files Modified

### Backend
- `app/jobs/stripe_webhook_job.rb` - Added nil-safe Time handling
- `app/controllers/subscriptions_controller.rb` - Added immediate subscription creation on success

### Frontend
- `app/views/subscriptions/show.html.erb` - Created subscription details view
- `app/views/subscriptions/new.html.erb` - Created plan selection view

### Tests
- `spec/jobs/stripe_webhook_job_spec.rb` - Added tests for nil timestamp handling

## Testing

All webhook tests pass with nil-safe handling:
```
StripeWebhookJob
  with nil timestamps (critical bug fix)
    ✓ handles nil timestamps without raising TypeError
    ✓ creates subscription with default timestamps when nil
  subscription.updated with nil timestamps
    ✓ keeps existing timestamps when update has nil
```

## Security Validation

✅ Fetching from Stripe API using session_id is secure (user-scoped)
✅ Idempotent using find_or_initialize_by
✅ Webhook still processes as backup
✅ No security risk - user can only fetch their own session

## Deployment Notes

1. Deploy webhook fix immediately to prevent subscription creation failures
2. Monitor webhook processing for any nil timestamp errors
3. Views are backward compatible with existing subscription data