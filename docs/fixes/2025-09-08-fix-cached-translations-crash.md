# 2025-09-08 Fix: Nil @cached_translations Crash and False Permission Triggers

## Issue
**Linear Issue**: UNL-100  
**Sentry Issue**: UNLISTERS-7  
**Impact**: JavaScript autosave errors on every form save for users without paid subscriptions

## Problem Description
Users without paid subscriptions were experiencing errors on EVERY autosave when editing projects, even when they never touched the `summary_only` checkbox. The form uses JavaScript autosave that triggers on field changes.

Two issues were identified:
1. **False change detection**: The permission check was incorrectly detecting changes on every autosave
2. **Missing instance variables**: When permission was denied, the form crashed due to nil `@cached_translations`

## Root Cause Analysis

### Issue 1: False Change Detection (PRIMARY CAUSE)
Rails checkbox helpers generate "0"/"1" strings, but the comparison was checking against "false"/"true" strings:

```ruby
# OLD BROKEN CODE:
def changing_summary_only_setting?
  params.dig(:project, :summary_only).present? && 
    @project.summary_only.to_s != params[:project][:summary_only].to_s
end
```

Rails `f.check_box :summary_only` generates:
```html
<input type="hidden" name="project[summary_only]" value="0" />
<input type="checkbox" name="project[summary_only]" value="1" />
```

This caused false positives:
- Unchecked: sends "0" → compared to `false.to_s` = "false" → "0" != "false" = **change incorrectly detected**
- Checked: sends "1" → compared to `true.to_s` = "true" → "1" != "true" = **change incorrectly detected**

### Issue 2: Missing Instance Variables  
When permission was (incorrectly) denied, `render :edit` was called without initializing `@cached_translations`, causing the template to crash.

## Solution
Two-part fix addressing both the root cause and the symptom:

### Part 1: Fix the comparison logic (ROOT CAUSE)
```ruby
def changing_summary_only_setting?
  param_val = params.dig(:project, :summary_only)
  return false if param_val.blank?  # Handles nil and empty strings
  
  # Convert Rails checkbox "0"/"1" strings to actual boolean for comparison
  # Empty strings are cast to nil, which we handle with blank? check above
  param_as_boolean = ActiveModel::Type::Boolean.new.cast(param_val)
  
  @project.summary_only != param_as_boolean
end
```

**Note**: Using `blank?` instead of `nil?` also handles empty string parameters that could be sent in certain form submission scenarios, preventing another class of false positives.

### Part 2: Initialize instance variables (SAFETY NET)
```ruby
def ensure_summary_only_permission
  return true unless changing_summary_only_setting?
  
  if current_user.can_create_summary_only_projects?
    true
  else
    flash[:alert] = t('projects.errors.upgrade_required_for_summary_only')
    
    # Initialize required instance variables for edit view
    @project_auths = @project.project_auths.where(access_level: :full_details)
    @auth_requests = @project.connection_requests.where(status: :pending)
    setup_cached_translations
    
    render :edit, status: :unprocessable_entity
    false
  end
end
```

## Files Modified
- `app/controllers/projects_controller.rb` - Fixed `changing_summary_only_setting?` method and added instance variable initialization

## Testing
- Added comprehensive RSpec tests in `spec/requests/projects_controller_summary_only_permission_spec.rb`
- Tests verify autosave doesn't trigger false permission checks
- Tests confirm proper handling when permission is genuinely denied

## Verification
- ✅ Autosave no longer triggers false change detection
- ✅ Permission check only fires on actual changes  
- ✅ Edit form renders correctly when permission denied
- ✅ No more JavaScript errors on every autosave