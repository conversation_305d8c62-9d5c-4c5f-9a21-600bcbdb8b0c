# 2025-09-08 Email Notification Race Condition Fix

## Issue: UNL-54
Critical email notification failures due to GoodJob rate limiting race conditions.

## Problem Summary
- **Impact**: 50% of admin notifications and 0% of user approval notifications failing
- **Root Cause**: Race conditions in GoodJob's rate limiting with multi-threaded mailer queue
- **Secondary Issue**: BulkNotificationJob using deliver_now bypassed rate limiting entirely

## Solution Implemented

### Phase 1: Critical Fixes
1. **Single-threaded mailer queue** to eliminate race conditions
2. **Conservative rate limiting** (1 email/1.5 seconds) to avoid timing precision issues
3. **Fixed BulkNotificationJob** to use deliver_later instead of deliver_now
4. **Removed problematic bulk_new_project_notification** method that bypassed rate limiting

### Phase 2: Reliability Improvements
1. **EmailDeliveryTracker model** for idempotent email delivery
2. **Tracking system** prevents duplicate emails and provides audit trail
3. **EmailHealthCheckJob** for hourly monitoring and alerting
4. **Comprehensive test coverage** for all email delivery scenarios

## Technical Details

### Configuration Changes
```ruby
# config/initializers/good_job.rb
config.good_job.queues = 'mailers:1;default:3;*:5'  # Single thread for mailers

# app/jobs/rate_limited_mail_delivery_job.rb
good_job_control_concurrency_with(
  perform_throttle: [1, 1.5.seconds],
  key: 'resend_api_rate_limit',
  total_limit: 1
)
```

### New Models
- **EmailDeliveryTracker**: Tracks delivery status (pending → queued → sent/failed)
- Ensures idempotency through unique index on [project_id, user_id, email_type]

### Monitoring
- **EmailHealthCheckJob**: Runs hourly via cron
- Alerts when >10 failed deliveries or >20 stuck emails
- Logs comprehensive delivery statistics

## Testing
All new functionality covered by comprehensive RSpec tests:
- `spec/jobs/bulk_notification_job_spec.rb`
- `spec/jobs/email_health_check_job_spec.rb`
- `spec/models/email_delivery_tracker_spec.rb`
- Updated `spec/mailers/bulk_notification_spec.rb`

## Migration Notes
- Run `bin/rails db:migrate` to create email_delivery_trackers table
- Existing emails in queue will continue to work
- New emails will automatically use improved system

## Monitoring
- Check `/good_job` dashboard for job queue status
- Review EmailDeliveryTracker records for delivery audit
- Monitor logs for "EMAIL HEALTH CHECK ALERT" messages

## Future Considerations
- Redis/Sidekiq migration only needed if:
  - User base > 10,000 active users
  - Email volume > 100,000/day
  - Multiple external APIs with complex rate limits
- Current GoodJob solution sufficient for foreseeable scale