# Shape-Up Framework: Simplified Guide

This document outlines our simplified approach to the Shape-Up framework for product development.

## Core Concepts

*   **Appetite:** We define how much time we're willing to spend on a project *before* we start designing the solution. This is our budget. A typical appetite is a "small batch" (1-2 weeks) or a "big batch" (4 weeks).
*   **Shaping:** Instead of writing detailed specifications, we "shape" the work. This means defining the problem, the key elements of the solution, and the boundaries, while leaving the implementation details to the team. Shaped work is rough, not polished.
*   **Betting:** We hold a "betting table" to decide which shaped projects to take on for the next cycle. We are "betting" that the project, as shaped, can be completed within the appetite.
*   **Building:** A team has a full, uninterrupted cycle to build the project. For us, a cycle is **1 week**. The team has autonomy to make decisions and complete the work within the cycle.

## The Process

1.  **Shaping (Ongoing):**
    *   Anyone can identify a problem or idea.
    *   The idea is "shaped" into a pitch. A pitch includes:
        *   **Problem:** What problem are we trying to solve?
        *   **Appetite:** How much time are we willing to spend? (e.g., 1 week)
        *   **Solution:** A high-level overview of the solution. What are the key components? Where does it fit in the existing system?
        *   **Rabbit Holes:** What are the potential pitfalls or areas to avoid?
        *   **No-Gos:** What is explicitly out of scope?

2.  **Betting (Start of Cycle):**
    *   We review the pitches.
    *   We decide which bets to make for the upcoming cycle.

3.  **Building (The Cycle - 1 Week):**
    *   A small, focused team is assigned to the project.
    *   The team has the full week to build and deliver the shaped work.
    *   The goal is to have a finished, functional piece of work at the end of the cycle. It doesn't have to be perfect, but it must be done.

This framework is designed to be simple and flexible. We want to focus on shipping meaningful work in short cycles.
