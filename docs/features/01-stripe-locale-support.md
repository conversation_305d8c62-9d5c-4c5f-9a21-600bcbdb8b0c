# Stripe Locale Support

## Overview
Added locale parameter support to Stripe checkout sessions and billing portal to provide localized payment experiences matching the application's language settings.

## Implementation Details

### Files Modified
- `app/services/stripe_subscription_service.rb`
  - Added `locale: I18n.locale.to_s` to checkout session parameters (line 23)
  - Added `locale: I18n.locale.to_s` to billing portal session parameters (line 133)

### Test Coverage
- `spec/services/stripe_subscription_service_spec.rb` 
  - Tests for Slovak (sk) and English (en) locale in checkout sessions
  - Tests for Slovak (sk) and English (en) locale in billing portal sessions
  - Tests for locale consistency with user profile language preferences

## How It Works

1. **Language Detection**: The application determines the user's language preference through:
   - User profile `default_language` setting (if user is signed in)
   - URL locale parameter
   - Default locale (Slovak - sk)

2. **Stripe Integration**: When creating checkout sessions or billing portal sessions:
   - The current `I18n.locale` value is passed to <PERSON>e
   - <PERSON><PERSON> automatically displays UI in the requested language
   - Email receipts are sent in the same language

3. **Supported Languages**:
   - Slovak (sk) - Application default
   - English (en) - Fallback language
   - Both are fully supported by Stripe

## Benefits

1. **Consistent User Experience**: Users see payment interfaces in their preferred language
2. **Email Localization**: Stripe receipts and notifications match the checkout language
3. **Automatic Fallback**: If a locale isn't supported, Stripe falls back to browser detection

## Testing

To verify the implementation:

1. **For Slovak users**:
   - Set user profile language to Slovak
   - Navigate to subscription page
   - Confirm Stripe checkout appears in Slovak

2. **For English users**:
   - Set user profile language to English  
   - Navigate to subscription page
   - Confirm Stripe checkout appears in English

3. **Billing Portal**:
   - Access customer billing portal with different locale settings
   - Verify portal language matches application locale

## Special Handling for 100% Discount Coupons

**Problem**: Stripe automatically generates promotional text like "Then X per year starting next year" when applying 100% discount coupons to subscriptions. This text doesn't properly localize to all languages.

**Solution**: For 100% discount coupons (like START), we switch to one-time payment mode instead of subscription mode. This eliminates the problematic promotional text entirely.

**Implementation**:
- When coupon code is "START" and provides 100% discount, the system switches to `mode: 'payment'`
- A localized custom message explains this is a one-time payment with no automatic renewal
- Partial discounts continue to use subscription mode normally
- Custom text messages in Slovak and English inform users about the payment structure

**I18n Translation Keys**:
- Key: `subscriptions.stripe.one_time_payment_notice`
- **Slovak**: "Toto je jednorazová platba a po skončení ročnej licencie nebudú účtované žiadne ďalšie poplatky."
- **English**: "This is one time payment and after annual license finish there will be no further payment charged."

**Benefits**:
- Completely eliminates untranslated promotional text
- Provides clear, localized communication about payment terms
- Maintains Stripe's understanding of merchant intent (preventing misuse of discount translations)
- Users receive exactly what they expect: one year of service with no automatic renewals

## Technical Notes

- Locale is set via `ApplicationController#set_locale` (lines 90-99)
- User language preference stored in `user_profile.default_language`
- Stripe automatically handles unsupported locales with graceful fallback
- No additional dependencies or configuration required