# 2025-09-09 Stripe Webhook Environment Switch

## Problem
We have two Stripe webhook endpoints for test mode:
1. **Local Development**: `localhost:5000/stripe_webhooks`
2. **Production Testing**: `https://app.unlisters.com/webhooks/stripe`

Both endpoints use different webhook secrets for signature verification, but the system was only configured with one webhook secret.

## Solution
Updated the Stripe initializer to automatically select the correct webhook secret based on the environment:
- **Development/Test Environment**: Uses `webhook_secrets.local`
- **Production Environment**: Uses `webhook_secrets.production_test`

### Implementation Details

1. **Updated Stripe Initializer** (`config/initializers/stripe.rb`):
   - Modified webhook secret selection logic to be environment-aware
   - Added fallback to legacy `webhook_secret` key for backward compatibility
   - Production environment automatically uses production webhook secret
   - Development/test environments use local webhook secret

2. **Added Route Compatibility** (`config/routes.rb`):
   - Added `/stripe_webhooks` route for local development compatibility
   - Kept existing `/webhooks/stripe` route for production
   - Both routes point to the same controller action

3. **Credentials Structure**:
   ```yaml
   stripe:
     test:
       webhook_secrets:
         local: whsec_... # For local development
         production_test: whsec_... # For production test mode
   ```

## Testing
- Added comprehensive test suite in `spec/initializers/stripe_webhook_environment_spec.rb`
- Tests verify correct webhook secret selection in all environments
- Both local and production endpoints work with their respective secrets

## Benefits
- No manual configuration needed when deploying
- Local development continues to work with `stripe listen --forward-to localhost:5000/stripe_webhooks`
- Production automatically uses the correct webhook secret
- Eliminates webhook signature verification errors due to wrong secret

## Backward Compatibility
- Falls back to legacy `webhook_secret` key if new structure not present
- Both webhook endpoints are available for maximum compatibility