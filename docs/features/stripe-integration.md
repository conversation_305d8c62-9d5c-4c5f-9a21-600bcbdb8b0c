# Stripe Integration - Webhooks & Payment Processing

## Overview

This implementation provides a complete Stripe integration for subscription management in the Unlisters platform, including webhook processing, payment handling, and subscription lifecycle management.

## Architecture

### Components

1. **Webhook Controller** (`StripeWebhooksController`)
   - Receives and validates Stripe webhook events
   - Verifies webhook signatures for security
   - Queues events for asynchronous processing

2. **Webhook Processing Job** (`StripeWebhookJob`)
   - Processes webhook events asynchronously with retry logic
   - Handles subscription lifecycle events
   - Updates local database based on Stripe events
   - Maintains data consistency with transactions

3. **Customer Creation Job** (`CreateStripeCustomerJob`)
   - Creates Stripe customers asynchronously after user registration
   - Ensures resilient user signup that doesn't fail on Stripe API issues
   - Handles pending subscriptions from referrals

4. **Subscription Service** (`StripeSubscriptionService`)
   - Manages checkout sessions for new subscriptions
   - Handles subscription updates (upgrades/downgrades)
   - Manages cancellations and reactivations
   - Provides billing portal access

5. **Subscriptions Controller** (`SubscriptionsController`)
   - User-facing subscription management interface
   - Handles pricing display and plan selection
   - Manages checkout flow and success/cancel callbacks

6. **Subscription Policy** (`SubscriptionPolicy`)
   - Authorization rules for subscription features
   - Tier-based access control (free/premium/pilot)
   - Usage limit enforcement

## Database Schema

### Users Table Addition
- `stripe_customer_id` (string, unique index) - Links user to Stripe customer

### Subscriptions Table (existing)
- Full subscription history with Stripe integration fields
- Supports multiple payment providers (Stripe, Paddle, LemonSqueezy)
- Tracks status, periods, trials, and cancellations

## Configuration

### Required Credentials

Add to Rails credentials (`rails credentials:edit`):

```yaml
stripe:
  secret_key: sk_test_... # or sk_live_... for production
  webhook_secret: whsec_...
  product_id: prod_... # Optional: for filtering prices
```

Or use environment variables:
- `STRIPE_SECRET_KEY`
- `STRIPE_WEBHOOK_SECRET`

### Webhook Setup

1. In Stripe Dashboard, add webhook endpoint:
   - URL: `https://your-domain.com/webhooks/stripe`
   - Events to listen for:
     - `customer.subscription.created`
     - `customer.subscription.updated`
     - `customer.subscription.deleted`
     - `customer.subscription.trial_will_end`
     - `invoice.payment_succeeded`
     - `invoice.payment_failed`
     - `customer.created`
     - `customer.updated`

2. Copy the webhook signing secret to credentials

## Usage

### User Flow

1. **Subscription Creation**
   - User visits `/subscriptions` to see pricing
   - Selects a plan and clicks subscribe
   - Redirected to Stripe Checkout
   - After payment, webhook creates subscription record
   - User redirected back to success page

2. **Subscription Management**
   - View current subscription at `/subscription`
   - Access billing portal for payment method updates
   - Upgrade/downgrade through the UI
   - Cancel with option for immediate or end-of-period

3. **Authorization**
   - Use in controllers:
     ```ruby
     authorize! current_user, to: :premium_feature?, with: SubscriptionPolicy
     ```
   - Check specific features:
     ```ruby
     policy = SubscriptionPolicy.new(user: current_user)
     if policy.access_feature?('advanced_analytics')
       # Show feature
     end
     ```

## Security Considerations

1. **Webhook Security**
   - Signature verification prevents replay attacks
   - CSRF protection disabled only for webhook endpoint
   - All events processed asynchronously to prevent timeouts

2. **Data Protection**
   - No payment card data stored locally
   - Stripe customer IDs indexed and unique
   - All sensitive operations in database transactions

3. **Error Handling**
   - Exponential backoff retries for failed jobs
   - Graceful handling of missing records
   - Comprehensive logging for debugging

## Testing

### Running Tests

```bash
# Run webhook controller tests
bundle exec rspec spec/controllers/stripe_webhooks_controller_spec.rb

# Run webhook job tests
bundle exec rspec spec/jobs/stripe_webhook_job_spec.rb

# Run all subscription-related tests
bundle exec rspec spec/**/*subscription*_spec.rb
```

### Testing with Stripe CLI

1. Install Stripe CLI
2. Login: `stripe login`
3. Forward webhooks to local: 
   ```bash
   stripe listen --forward-to localhost:5000/webhooks/stripe
   ```
4. Trigger test events:
   ```bash
   stripe trigger customer.subscription.created
   ```

## Monitoring

### Key Metrics to Track
- Webhook processing success/failure rates
- Subscription conversion rates
- Payment failure rates
- Average processing time for webhooks

### Logging
- All webhook events logged with type and ID
- Subscription state changes logged
- Payment successes and failures tracked
- Errors logged with full context

## Future Enhancements

1. **Email Notifications**
   - Implement UserMailer methods for:
     - Trial ending notifications
     - Payment failure alerts
     - Subscription confirmation emails

2. **Usage-Based Billing**
   - Track usage metrics
   - Report usage to Stripe
   - Implement metered billing for pilot tier

3. **Advanced Features**
   - Multiple subscriptions per user
   - Subscription pausing
   - Custom trial periods
   - Discount codes and promotions

4. **Analytics Dashboard**
   - MRR tracking
   - Churn analysis
   - Customer lifetime value
   - Conversion funnel metrics

## Troubleshooting

### Common Issues

1. **Webhook Not Receiving Events**
   - Check webhook URL in Stripe dashboard
   - Verify webhook secret in credentials
   - Check server logs for incoming requests

2. **Subscription Not Created**
   - Verify user has stripe_customer_id
   - Check webhook job logs
   - Ensure Plan records exist with matching price IDs

3. **Payment Failures**
   - Check Stripe dashboard for decline reasons
   - Verify customer has valid payment method
   - Review retry settings in Stripe

### Debug Commands

```ruby
# Rails console debugging
user = User.find_by(email: '<EMAIL>')
user.stripe_customer_id
user.active_subscription
user.subscriptions

# Check webhook processing
StripeWebhookJob.perform_now(event_data)

# Test subscription service
service = StripeSubscriptionService.new(user)
service.get_active_stripe_subscription
```

## Dependencies

- `stripe` gem (~> 13.3)
- GoodJob for background job processing
- ActionPolicy for authorization

## Migration Path

Run migration to add Stripe customer ID to users:
```bash
rails db:migrate
```

Existing users will get Stripe customers created on their next login or when they attempt to subscribe.