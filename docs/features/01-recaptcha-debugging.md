# reCAPTCHA Production Issue Diagnosis

## Problem Summary
Registration attempts on production are failing with reCAPTCHA verification errors. The system redirects back to the sign-up page immediately after form submission.

## Root Cause Analysis

### Observed Behavior
From production logs:
- User submits registration form with email `<EMAIL>`
- Form includes `recaptcha_token` parameter (shown as [FILTERED])
- Controller immediately redirects to sign-up page (302 response in 2ms)
- This indicates `verify_recaptcha` method returned false

### Potential Issues

1. **Missing or Invalid Credentials**
   - The production environment may have incorrect or missing reCAPTCHA credentials
   - Site key and secret key mismatch

2. **Domain Mismatch**
   - reCAPTCHA keys are domain-specific
   - Production domain (app.unlisters.com) must be registered in Google reCAPTCHA console

3. **Token Generation Issues**
   - JavaScript may be failing to properly generate token
   - Race condition between form submission and token generation

4. **API Communication Failure**
   - Network issues reaching Google's API
   - SSL/TLS certificate issues

## Immediate Actions Taken

### 1. Enhanced Logging
Added comprehensive logging to `registrations_controller.rb`:
- Token presence and length
- API response codes
- Success/failure details with scores
- Error codes from Google API

### 2. Debug Improvements
- Created debug script for testing reCAPTCHA verification
- Added detailed error messages to help diagnose issues

## Recommended Next Steps

### 1. Verify Production Credentials
```bash
# On production server
rails console
Rails.application.credentials.recaptcha[:site_key].present?
Rails.application.credentials.recaptcha[:secret_key].present?
```

### 2. Check Domain Registration
1. Log into Google reCAPTCHA Admin Console: https://www.google.com/recaptcha/admin
2. Verify that `app.unlisters.com` is listed in allowed domains
3. Ensure using reCAPTCHA v3 (not v2)

### 3. Test Token Generation
Add temporary console logging to frontend:
```javascript
grecaptcha.execute('<site_key>', {action: 'registration'}).then(function(token) {
  console.log('Token generated:', token.substring(0, 20) + '...');
  // existing code
});
```

### 4. Temporary Workaround Options

#### Option A: Disable reCAPTCHA temporarily
```ruby
# In registrations_controller.rb, line 7
if false && Rails.application.credentials.recaptcha&.dig(:secret_key).present?
```

#### Option B: Add bypass for specific emails
```ruby
def verify_recaptcha
  # Temporary bypass for testing
  bypass_emails = ['<EMAIL>']
  if bypass_emails.include?(params.dig(:user, :email))
    Rails.logger.info "Bypassing reCAPTCHA for authorized test email"
    return true
  end
  
  # existing code...
end
```

### 5. Monitor New Logs
After deployment, monitor for new log entries:
```bash
tail -f log/production.log | grep -i recaptcha
```

Expected log entries will show:
- Token length
- API response code (should be 200)
- Success status and score
- Any error codes

## Common reCAPTCHA Error Codes

- `missing-input-secret`: The secret key is missing
- `invalid-input-secret`: The secret key is invalid
- `missing-input-response`: The token is missing
- `invalid-input-response`: The token is invalid or expired
- `bad-request`: The request is invalid
- `timeout-or-duplicate`: Token has expired or been used already

## Security Considerations

- Never log the full token or secret key
- Ensure credentials are properly encrypted in Rails credentials
- Consider implementing rate limiting as additional protection
- Monitor for suspicious registration patterns

## Testing Checklist

- [ ] Verify credentials are present in production
- [ ] Confirm domain is registered in reCAPTCHA console  
- [ ] Check browser console for JavaScript errors
- [ ] Review production logs with enhanced logging
- [ ] Test with different browsers/devices
- [ ] Verify SSL certificates are valid
- [ ] Check firewall rules allow outbound HTTPS to Google

## Deployment Instructions

1. Deploy the enhanced logging version
2. Monitor logs during next registration attempt
3. Based on specific error, apply appropriate fix
4. Consider temporary bypass if urgent user registrations needed