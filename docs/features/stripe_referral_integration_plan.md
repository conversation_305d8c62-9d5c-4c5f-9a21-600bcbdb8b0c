# Stripe Referral Code Integration Plan

## 1. Current State Analysis

The application currently has two parallel, disconnected systems for user benefits:

1.  **Internal Referral System:**
    *   **Model:** `ReferralCode` exists but lacks fields for discount amounts or percentages.
    *   **Controller:** `ReferralCodesController` handles user-facing redemption.
    *   **Flow:** The `redeem` action directly updates the `User` model's `tier` and `subscription_type` attributes, granting them premium access without any interaction with <PERSON><PERSON>. This is a manual, off-platform subscription grant.
    *   **UI:** A view exists at `app/views/referral_codes/new.html.erb` for users to enter a code.

2.  **Stripe Subscription System:**
    *   **Service:** `StripeSubscriptionService` manages the creation of Stripe Checkout sessions for paid subscriptions.
    *   **Flow:** Users are redirected to a standard Stripe Checkout page to enter payment details.
    *   **Discount Support:** The service is configured with `allow_promotion_codes: true`, meaning users can manually enter Stripe-generated promotion codes on the Stripe Checkout page. There is no mechanism to apply these discounts programmatically from our application.

**The core problem is the lack of connection between these two systems.** A user redeeming an internal `ReferralCode` does not get a Stripe subscription, which complicates tracking, analytics, and future billing.

## 2. Proposed Solution: A Fully Integrated System

The goal is to make Stripe the single source of truth for all subscriptions, including those granted via referral codes. This simplifies application logic and provides accurate subscription data.

The plan is to connect the internal `ReferralCode` to a corresponding Stripe Coupon. When a user redeems a code, we will apply the Stripe Coupon to their Stripe Checkout session programmatically.

This involves three main parts:
1.  **Data Layer:** Enhance the `ReferralCode` model to store Stripe coupon details.
2.  **Admin & Sync:** Create Stripe coupons when admins create referral codes.
3.  **User Flow:** Modify the redemption and checkout flow to apply the Stripe coupon.

## 3. Step-by-Step Implementation Plan

### Step 1: Enhance the `ReferralCode` Model

**Goal:** Add the necessary database column to link referral codes to Stripe coupons.

1.  **Generate Migration:** Create a new migration to add `stripe_coupon_id` to the `referral_codes` table.
    ```bash
    rails g migration AddStripeCouponIdToReferralCodes stripe_coupon_id:string
    ```
2.  **Run Migration:**
    ```bash
    rails db:migrate
    ```
3.  **Update Model:** Add a validation to `app/models/referral_code.rb` to ensure the ID is unique if it exists.
    ```ruby
    # app/models/referral_code.rb
    validates :stripe_coupon_id, uniqueness: true, allow_nil: true
    ```

### Step 2: Synchronize Referral Codes with Stripe Coupons

**Goal:** Programmatically create a Stripe Coupon whenever an admin creates a `ReferralCode` that is meant to provide a discount.

1.  **Update Admin UI:**
    *   Modify the form at `app/views/admin/referral_codes/_form.html.erb`.
    *   Add fields for `percent_off` (e.g., a number input) and `duration` (e.g., a dropdown with 'once', 'repeating', 'forever'). These fields will be used to create the coupon in Stripe.

2.  **Create a Stripe Coupon Service:**
    *   Create a new service file: `app/services/stripe_coupon_service.rb`.
    *   This service will contain the logic to communicate with the Stripe API.

    ```ruby
    # app/services/stripe_coupon_service.rb
    class StripeCouponService
      def initialize(referral_code)
        @referral_code = referral_code
      end

      def create_coupon
        # Only create if there's a percent_off and no coupon already exists
        return unless @referral_code.percent_off.to_i > 0 && @referral_code.stripe_coupon_id.blank?

        begin
          coupon = Stripe::Coupon.create(
            percent_off: @referral_code.percent_off,
            duration: @referral_code.duration, # 'once', 'repeating', or 'forever'
            name: "Referral Code: #{@referral_code.code}"
          )
          @referral_code.update!(stripe_coupon_id: coupon.id)
        rescue Stripe::StripeError => e
          Rails.logger.error "Stripe Coupon creation failed for ReferralCode #{@referral_code.id}: #{e.message}"
          # Optionally, add error to the model so it's visible in the admin UI
          @referral_code.errors.add(:base, "Stripe Coupon creation failed: #{e.message}")
          raise ActiveRecord::RecordInvalid, @referral_code
        end
      end
    end
    ```
    *Note: This requires adding `percent_off` and `duration` attributes to the `ReferralCode` model via another migration.*

3.  **Integrate Service with Model:**
    *   Add an `after_save` callback to the `ReferralCode` model to trigger the coupon creation.

    ```ruby
    # app/models/referral_code.rb
    after_save :sync_with_stripe, if: -> { (saved_change_to_percent_off? || saved_change_to_duration?) && percent_off.to_i > 0 }

    private

    def sync_with_stripe
      StripeCouponService.new(self).create_coupon
    end
    ```

### Step 3: Modify the User Redemption and Checkout Flow

**Goal:** Instead of manually upgrading the user, redirect them to the Stripe checkout with the discount applied.

1.  **Update `SubscriptionsController` (or create it):**
    *   The primary user flow should be directed to a subscription selection page. Let's assume this is `SubscriptionsController#new`.
    *   This controller will be responsible for initiating the checkout.

2.  **Update `StripeSubscriptionService`:**
    *   Modify the service to accept a `stripe_coupon_id`.

    ```ruby
    # app/services/stripe_subscription_service.rb
    # ... existing code ...
    def create_checkout_session(user, plan_id, stripe_coupon_id = nil)
      # ...
      session_params = {
        # ... other params
      }

      if stripe_coupon_id.present?
        session_params[:discounts] = [{ coupon: stripe_coupon_id }]
      else
        # This is important to still allow manual promo codes on Stripe's page
        session_params[:allow_promotion_codes] = true
      end

      Stripe::Checkout::Session.create(session_params)
    end
    ```

3.  **Update `ReferralCodesController#redeem`:**
    *   Change the `redeem` action. Instead of updating the user's tier directly, it will now store the valid code in the session and redirect to the subscription page.

    ```ruby
    # app/controllers/referral_codes_controller.rb
    def redeem
      code = params[:referral_code][:code].strip
      referral_code = ReferralCode.find_by(code: code)

      if referral_code&.is_valid?
        # Store the coupon ID in the session to be used at checkout
        session[:redeemed_stripe_coupon_id] = referral_code.stripe_coupon_id
        flash[:notice] = "Referral code '#{code}' applied successfully!"
        redirect_to new_subscription_path # Redirect to the page where they choose a plan
      else
        flash[:alert] = "Invalid or expired referral code."
        render :new
      end
    end
    ```

4.  **Connect the Coupon to the Checkout:**
    *   In the `SubscriptionsController` action that initiates the checkout, pull the coupon ID from the session and pass it to the service.

    ```ruby
    # app/controllers/subscriptions_controller.rb
    def create
      # ... logic to get user and plan ...
      stripe_coupon_id = session.delete(:redeemed_stripe_coupon_id) # Use and remove from session

      checkout_session = StripeSubscriptionService.new.create_checkout_session(
        current_user,
        params[:plan_id],
        stripe_coupon_id
      )
      # ... redirect to session.url ...
    end
    ```
This plan provides a clear path to a robust and maintainable system by leveraging Stripe for all subscription management while keeping the user-facing referral code experience simple.
