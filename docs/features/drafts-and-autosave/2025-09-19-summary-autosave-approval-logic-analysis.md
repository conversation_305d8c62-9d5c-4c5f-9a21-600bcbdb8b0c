# Analysis of Summary Autosave and Approval Logic

**Date**: 2025-09-19

This document analyzes the logic behind excluding the `summary` (title) field from the autosave functionality in the project form and explores the edge cases related to the project approval workflow.

### Why the Current "Protection" Is Justified (The Conservative Approach)

The original developer's choice to exclude `:summary` from autosave is a very safe, if blunt, way to prevent a critical business rule from being bypassed. The core of the problem lies in how Rails tracks changes.

1.  **The `summary_changed?` Problem:** The approval logic likely relies on Active Record's dirty tracking, specifically a method like `summary_changed?`. This method checks if the `summary` attribute has been modified *in memory* since the last time the record was loaded or saved.

2.  **How Autosave Breaks This Logic:**
    *   A user edits the title of a **published** project.
    *   The `autosave` event fires, sending the new title to the server.
    *   **If autosave were enabled for the title**, the server would save the new title to the database.
    *   Crucially, this action would "clean" the project record. The `summary_changed?` flag would now be `false`, because the most recent change has been persisted.
    *   When the user finally clicks the main "Update Project" button, the controller's re-approval check (`if @project.published? && @project.summary_changed?`) would fail because `summary_changed?` is `false`.
    *   **Result:** The title of a published, approved project has been changed without triggering the required re-approval process. This is a significant integrity violation.

By completely blocking the title from autosave, the developer ensured that the `summary_changed?` flag would only be set when the user explicitly clicks the main "Update" button, guaranteeing the re-approval check works as intended.

### Hypothesis: "Trigger only if the project is in a publish state?"

This is correct. The re-approval logic is only necessary for projects that are already **published and approved**. For a brand new project or a draft, changing the title has no bearing on an approval status that doesn't exist yet.

The current implementation is overly cautious because it applies the same rule to both drafts and published projects, leading to a poor user experience where the title is lost on reload after a file upload.

### Edge Cases and Scenarios to Consider

Here are the edge cases that arise if we were to simply allow `:summary` in `autosave_params`.

#### Case 1: New Draft Project (The Safe Scenario)
*   **Action:** A user creates a new project. They type a title. Autosave fires.
*   **If Title Is Autosaved:** The project is saved with the title. The `summary_changed?` flag is reset.
*   **Action:** The user clicks "Publish".
*   **Server Logic:** The controller checks for re-approval. The condition `if @project.published?` is `false`, so the check is skipped. The project goes into the approval queue for the first time.
*   **Verdict:** This is perfectly safe. The current protection is unnecessarily restrictive in this case.

#### Case 2: Editing a Published Project (The Dangerous Scenario)
*   **Action:** A user edits the title of a published and approved project. Autosave fires.
*   **If Title Is Autosaved:** The new title is immediately saved to the database. The change is live. The `summary_changed?` flag is reset.
*   **Problem:** The re-approval workflow has been completely bypassed. The user could close the tab, and the "unapproved" change would remain live. This is the primary risk the current implementation prevents.

#### Case 3: The "Accidental" Publish
*   **Action:** A user is editing a draft. They type a title, which is autosaved.
*   **Action:** They then click the "Publish" toggle/button. The form submits.
*   **Server Logic:** The controller receives all params, including the `summary` and the `project_status: 'true'`.
*   **Verdict:** This is also safe. It's a first-time publish, so the re-approval logic isn't relevant.

#### Case 4: The "Bait and Switch"
*   **Action:** A user edits a published project's title. Autosave saves the new, potentially problematic title (e.g., "AMAZING NEW DEAL!!!").
*   **Action:** Before the admin can see it, the user changes the title back to the original, which is also autosaved.
*   **Problem:** While the final state is okay, there was a window of time where the project had an unapproved title. This could be minor or significant depending on how titles are used across the platform (e.g., in cached views, search indexes, or marketing emails).

### Conclusion

The existing protection is a heavy-handed but effective solution to a real and critical problem: **preventing the autosave mechanism from clearing the `_changed?` attribute, which would allow users to bypass the re-approval process for published projects.**

The ideal solution is to make the server-side logic smarter:

*   **Allow `:summary` in `autosave_params`.**
*   **Modify the `update` action's approval logic.** Instead of relying on `summary_changed?`, it should explicitly compare the incoming title from the params with the title currently in the database *before* saving.

This would allow the title to be safely autosaved for drafts while still correctly triggering re-approval for published projects, solving both the UX issue and the business rule integrity problem.
