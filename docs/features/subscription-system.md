# Subscription System Architecture

## Overview

The Unlisters application implements a comprehensive subscription system that:
- Mirrors Stripe's data model for seamless payment integration
- Maintains complete subscription history for analytics
- Supports multiple payment providers (<PERSON><PERSON>, Paddle, LemonSqueezy, Manual)
- Provides backwards compatibility with legacy user tier attributes

## Core Components

### 1. Plan Model (`app/models/plan.rb`)

Represents subscription tiers and pricing configurations.

**Key Features:**
- Three tiers: `free`, `premium`, `pilot`
- Three intervals: `one_time`, `month`, `year`
- Multi-provider support with separate ID fields for each provider
- Metadata storage for provider-specific data

**Database Schema:**
```ruby
create_table :plans do |t|
  t.string :name, null: false
  t.integer :tier, null: false # free: 0, premium: 1, pilot: 2
  t.integer :interval, null: false # one_time: 0, month: 1, year: 2
  t.integer :price_cents, null: false, default: 0
  
  # Provider sync fields
  t.string :stripe_price_id
  t.string :stripe_product_id
  t.string :paddle_price_id
  t.string :lemonsqueezy_variant_id
  
  t.boolean :active, default: true
  t.json :metadata
  t.timestamps
end
```

### 2. Subscription Model (`app/models/subscription.rb`)

Tracks user subscription history with full Stripe compatibility.

**Key Features:**
- Stripe-compatible status enum (active, trialing, past_due, canceled, unpaid, incomplete, incomplete_expired)
- Full billing period tracking (start/end, trial periods)
- Provider data caching in JSON
- Cancellation and reactivation support

**Database Schema:**
```ruby
create_table :subscriptions do |t|
  t.references :user, null: false
  t.references :plan, null: false
  
  t.integer :status, null: false, default: 0
  
  # Billing periods
  t.datetime :current_period_start
  t.datetime :current_period_end
  t.datetime :trial_start
  t.datetime :trial_end
  t.datetime :canceled_at
  t.datetime :ended_at
  
  # Provider sync fields
  t.string :stripe_subscription_id
  t.string :stripe_customer_id
  t.string :paddle_subscription_id
  t.string :lemonsqueezy_subscription_id
  
  t.integer :payment_provider, default: 3
  t.json :provider_data
  
  # Metadata
  t.string :cancel_reason
  t.boolean :cancel_at_period_end, default: false
  
  t.timestamps
end
```

### 3. User Model Updates

The User model now supports both the new subscription system and legacy tier attributes.

**New Associations:**
```ruby
has_many :subscriptions, dependent: :destroy
has_one :active_subscription, -> { where(status: [:active, :trialing]) }, class_name: 'Subscription'
has_many :canceled_subscriptions, -> { where(status: :canceled) }, class_name: 'Subscription'
has_many :plans, through: :subscriptions
```

**Helper Methods:**
- `current_plan` - Returns the active subscription's plan
- `subscription_history` - Returns all subscriptions ordered by creation date
- `ever_subscribed?` - Checks if user has any subscription records
- `active_tier` - Returns the current tier name or 'free'

**Backwards Compatibility:**
The system maintains the legacy `subscription_tier` and `subscription_expires_at` fields on the User model. The `active_subscription?` and `subscription_expired?` methods check both the new subscription model and legacy fields.

## Data Migration

### Existing User Migration

The `MigrateUserSubscriptionData` migration (`db/migrate/20250819074617_migrate_user_subscription_data.rb`) handles the transition:

1. Creates Plan records if they don't exist
2. Maps existing user tiers to appropriate plans
3. Creates Subscription records preserving historical data
4. Maintains legacy fields for backwards compatibility

**Migration Logic:**
- Free tier users → Free plan subscription
- Premium users with active subscription → Premium Monthly/Annual based on expiration
- Expired premium users → Canceled Premium Monthly subscription
- Pilot users → Pilot Lifetime subscription

## Admin Interface

### Subscription Management (`app/controllers/admin/subscriptions_controller.rb`)

**Features:**
- View users by subscription status (free, premium, pilot, active, expired)
- View individual user subscription history
- Create manual subscriptions
- Cancel/reactivate subscriptions
- Legacy tier management support

**Available Actions:**
- `index` - List users with filtering by subscription status
- `show` - View user's complete subscription history
- `create_subscription` - Manually create a subscription
- `cancel_subscription` - Cancel a subscription (immediate or at period end)
- `reactivate_subscription` - Reactivate a canceled subscription
- `update_tier` - Legacy tier update with optional subscription creation

## Webhook Integration (Future)

The system is designed for seamless webhook integration:

```ruby
# Pseudo-code for future webhook handler
def handle_stripe_webhook(event)
  case event.type
  when 'customer.subscription.created'
    subscription = Subscription.create_from_stripe!(event.data.object)
  when 'customer.subscription.updated'
    subscription = Subscription.find_by!(stripe_subscription_id: event.data.object.id)
    subscription.sync_with_stripe!(event.data.object)
  when 'customer.subscription.deleted'
    subscription = Subscription.find_by!(stripe_subscription_id: event.data.object.id)
    subscription.update!(status: :canceled, ended_at: Time.current)
  end
end
```

## Subscription States

### Status Flow

```
trialing → active → past_due → canceled
                  ↘           ↗
                    unpaid
                  
incomplete → incomplete_expired
```

### Status Definitions

- **active**: Subscription is active and features are accessible
- **trialing**: In trial period, features are accessible
- **past_due**: Payment failed but grace period active
- **canceled**: Subscription canceled, no feature access
- **unpaid**: Payment required, no feature access
- **incomplete**: Initial payment pending
- **incomplete_expired**: Initial payment window expired

## Usage Examples

### Creating a Subscription

```ruby
# Manual subscription creation
subscription = user.subscriptions.create!(
  plan: Plan.find_by(tier: 'premium', interval: 'month'),
  status: :active,
  payment_provider: :manual,
  current_period_start: Time.current,
  current_period_end: 1.month.from_now
)
```

### Checking User Access

```ruby
# Check if user has active subscription
if user.active_subscription&.can_access_features?
  # Grant premium features
end

# Get user's current tier
tier = user.active_tier # Returns 'free', 'premium', or 'pilot'

# Check subscription history
user.subscription_history.each do |subscription|
  puts "#{subscription.plan.name}: #{subscription.display_status}"
end
```

### Canceling a Subscription

```ruby
# Cancel at period end (user retains access until expiration)
subscription.cancel!(reason: "Too expensive", at_period_end: true)

# Cancel immediately
subscription.cancel!(reason: "Refund requested", at_period_end: false)

# Reactivate if possible
subscription.reactivate! if subscription.can_reactivate?
```

## Testing

### Model Tests

- `spec/models/plan_spec.rb` - Plan model validations, scopes, and methods
- `spec/models/subscription_spec.rb` - Subscription lifecycle, status management, provider integration

### Running Tests

```bash
# Run all subscription-related tests
bundle exec rspec spec/models/plan_spec.rb spec/models/subscription_spec.rb

# Run with coverage
COVERAGE=true bundle exec rspec spec/models/subscription_spec.rb
```

## Seed Data

The system includes seed data for all plan types:

```ruby
# db/seeds.rb
plans_data = [
  { name: "Free", tier: "free", interval: "one_time", price_cents: 0 },
  { name: "Premium Monthly", tier: "premium", interval: "month", price_cents: 999 },
  { name: "Premium Annual", tier: "premium", interval: "year", price_cents: 9999 },
  { name: "Premium Lifetime", tier: "premium", interval: "one_time", price_cents: 29999 },
  { name: "Pilot Monthly", tier: "pilot", interval: "month", price_cents: 0 },
  { name: "Pilot Annual", tier: "pilot", interval: "year", price_cents: 0 },
  { name: "Pilot Lifetime", tier: "pilot", interval: "one_time", price_cents: 0 }
]
```

## Future Enhancements

### Payment Processing (UNL-73)

Future integration will include:
- Stripe webhook endpoints
- Payment method management
- Invoice generation
- Subscription upgrade/downgrade flows
- Proration handling

### Analytics

The subscription history enables:
- Churn rate calculation
- Lifetime value (LTV) analysis
- Upgrade/downgrade patterns
- Revenue forecasting
- Cohort analysis

## Security Considerations

### Authorization

- Only admins can manually create/modify subscriptions
- Users can only view their own subscription history
- Webhook endpoints require HMAC verification
- Provider IDs are unique and indexed

### Data Protection

- Payment provider data cached in JSON for audit trail
- Sensitive payment data never stored locally
- All subscription changes logged with timestamps
- Soft-delete pattern preserves history

## Troubleshooting

### Common Issues

1. **Duplicate subscription creation**
   - System cancels existing active subscription before creating new one
   - Check for race conditions in webhook handlers

2. **Legacy tier conflicts**
   - Both new subscriptions and legacy tiers are checked
   - New subscription model takes precedence when present

3. **Migration failures**
   - Ensure Plan records exist before migrating users
   - Migration is irreversible to preserve history

### Debug Commands

```ruby
# Rails console debugging
user = User.find_by(email: '<EMAIL>')

# Check subscription status
user.active_subscription
user.subscription_history.pluck(:status, :created_at)

# Force sync with provider
subscription = user.active_subscription
subscription.sync_with_stripe!(stripe_subscription_object)

# Check plan configuration
Plan.active.by_tier('premium')
Plan.find_by_provider_id('price_xyz')
```

## Implementation Checklist

- [x] Plan model and migration
- [x] Subscription model and migration
- [x] User model updates
- [x] Data migration for existing users
- [x] Admin interface updates
- [x] Model tests
- [x] Documentation
- [ ] Webhook endpoints (UNL-73)
- [ ] Payment processing (UNL-73)
- [ ] Customer portal
- [ ] Analytics dashboard