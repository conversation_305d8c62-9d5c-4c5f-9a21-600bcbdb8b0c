# 2025-09-08 - Pre-emptive Subscription Creation to Fix Race Condition

## Problem Statement

Previously, the Stripe integration was purely webhook-driven for subscription creation. When a user successfully paid, they were redirected back to our application, but the local `Subscription` record was only created when the corresponding webhook was received from <PERSON>e. This process could have a noticeable delay (race condition), leading to a poor user experience where a paying user would not see their subscription as active immediately, causing confusion.

## Solution: Create on Init Pattern

We implemented the "Create on Init" pattern, which involves creating a local `Subscription` record with an `incomplete` status *before* the user is redirected to the Stripe Checkout page. The webhook's role then shifts from creation to simply updating this existing record to an `active` state.

## Benefits

1. **Eliminates the user-facing race condition** - Users no longer see error messages after successful payment
2. **Provides immediate and accurate feedback** - The success page shows a processing message while waiting for the webhook
3. **Makes subscription lifecycle management more robust** - We always have a local record to track
4. **Improves resilience** - If webhooks are delayed, users still see appropriate messaging

## Implementation Details

### 1. Database Changes

The `subscriptions` table already had a `status` column with an enum that includes the `incomplete` status. No migration was needed.

### 2. Controller Changes

#### SubscriptionsController#create

- Now creates a local `Subscription` record with `status: :incomplete` before redirecting to Stripe
- Passes the subscription ID as `client_reference_id` to the Stripe checkout session
- Cleans up the subscription if Stripe checkout session creation fails

```ruby
# Create the local subscription record first with incomplete status
subscription = current_user.subscriptions.create!(
  plan: plan,
  status: :incomplete,
  payment_provider: :stripe
)

# Pass our subscription ID to Stripe
session = @subscription_service.create_checkout_session(
  checkout_price_id,
  subscription_success_url(plan_id: plan_id_for_url),
  subscription_cancel_url,
  coupon_code: checkout_coupon_code,
  client_reference_id: subscription.id
)
```

#### SubscriptionsController#success

- Finds the local subscription using `client_reference_id` from the Stripe session
- If the subscription is still `incomplete`, renders a processing page with JavaScript polling
- If the webhook was fast and subscription is already active, redirects normally

### 3. Webhook Job Changes

#### New Handler: checkout.session.completed

- This event fires immediately after successful payment
- Updates the incomplete subscription with `stripe_subscription_id` and `stripe_customer_id`
- Prepares the record for the subsequent `customer.subscription.created` event

#### Updated Handler: handle_subscription_created

- Now checks if a subscription already exists with the given `stripe_subscription_id`
- If found, updates the existing incomplete record to active status
- If not found (fallback flow), creates a new subscription as before

### 4. New View: processing_subscription.html.erb

- Shows a friendly "Payment Successful! We are activating your subscription..." message
- Includes JavaScript that polls the new `check_status` endpoint every second
- Automatically redirects to the subscriptions page once the subscription becomes active
- Has a 30-second timeout to prevent infinite polling

### 5. New Endpoint: check_status

- Returns the subscription status as JSON for AJAX polling
- Ensures the user owns the subscription before returning status
- Used by the processing page to detect when the subscription becomes active

## User Experience Flow

1. User clicks "Subscribe" and enters payment details on Stripe Checkout
2. Upon successful payment, they're redirected to `/subscriptions/success`
3. If the webhook hasn't processed yet, they see a processing page with a spinner
4. JavaScript polls for status updates every second
5. Once the webhook processes and subscription becomes active, the page automatically redirects
6. User sees their active subscription on the subscriptions page

## Error Handling

- If Stripe checkout session creation fails, the incomplete subscription is cleaned up
- If the webhook never arrives, the subscription remains incomplete and can be investigated
- The polling has a 30-second timeout to prevent infinite waiting
- All error cases show appropriate messages to the user

## Testing

Created comprehensive test coverage including:

- `spec/requests/subscriptions_preemptive_creation_spec.rb` - Tests the new controller behavior
- `spec/jobs/stripe_webhook_checkout_session_spec.rb` - Tests the webhook handlers
- Tests verify the race condition is eliminated
- Tests ensure proper cleanup on errors
- Tests validate the fallback flow still works

## Backward Compatibility

The implementation maintains backward compatibility:

- Manual subscriptions created outside our checkout flow still work
- The fallback flow in `handle_subscription_created` ensures webhooks without pre-created subscriptions still function
- Existing active subscriptions are unaffected

## Monitoring

Key events to monitor:

1. Subscriptions stuck in `incomplete` status for more than 5 minutes
2. Webhooks failing to update subscription status
3. Users experiencing the 30-second timeout on the processing page

## Future Improvements

1. Add websockets for real-time status updates instead of polling
2. Implement retry logic for failed webhook processing
3. Add admin tools to manually transition stuck subscriptions
4. Consider adding email notifications for incomplete subscriptions