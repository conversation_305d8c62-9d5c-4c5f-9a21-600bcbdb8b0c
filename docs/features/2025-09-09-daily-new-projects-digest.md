# Daily New Projects Digest Email Feature

## Overview
This feature replaces instant email notifications for new projects with a consolidated daily digest email. All active users receive a single daily email summarizing all new projects from the last 24 hours, based on project visibility settings.

## Implementation Details

### Components

1. **Mailer Method**: `NotificationMailer#daily_new_projects_digest`
   - Accepts a user and count of new projects
   - Sends localized email based on user's language preference
   - Email templates in both HTML and text formats

2. **Background Job**: `DailyNewProjectsDigestJob`
   - Counts projects created in the last 24 hours (approved and published only)
   - Finds eligible users based on project visibility (semi_public, network_only)
   - Enqueues digest emails via the rate-limited mail delivery system
   - Includes error handling to continue processing if individual emails fail

3. **Scheduler Integration**: 
   - Rake task: `scheduler:send_daily_digest`
   - Render cron job configured to run at 8:30 AM UTC daily
   - Defined in `render.yaml` for production deployment

4. **Disabled Instant Notifications**:
   - Removed `BulkNotificationJob` call from `ProjectsController#update_approval`
   - Instant notifications no longer sent when projects are approved

### Eligible Recipients

Users must meet ALL of the following criteria:
- User account must be active and approved

### Email Content

The digest email includes:
- Personalized greeting with user's first name
- Count of new projects available
- Call-to-action to view projects
- Link to the projects listing page

### Configuration

**Cron Schedule**: Daily at 8:30 AM UTC
```yaml
crons:
  - name: daily-new-projects-digest
    schedule: "30 8 * * *"
    command: bundle exec rake scheduler:send_daily_digest
```

### Testing

Comprehensive test coverage includes:
- Correct recipient filtering (all active and approved users)
- Proper project counting (approved and published only)
- Error resilience (continues if individual emails fail)
- No emails sent when no new projects exist

### Manual Testing

1. **Run the rake task manually**:
   ```bash
   bundle exec rake scheduler:send_daily_digest
   ```

2. **Preview the email template**:
   Visit: `http://localhost:3000/rails/mailers/notification_mailer/daily_new_projects_digest`

3. **Run the job directly in console**:
   ```ruby
   DailyNewProjectsDigestJob.perform_now
   ```

### Monitoring

The job logs key information prefixed with `[DAILY_DIGEST]`:
- Start and end times
- Number of new projects found
- Number of eligible users
- Individual email queue status
- Any errors encountered

### Rollback Plan

To revert to instant notifications:
1. Uncomment the `BulkNotificationJob` call in `ProjectsController#update_approval`
2. Remove or disable the cron job in `render.yaml`
3. Deploy the changes

## Dependencies

- GoodJob for background job processing
- Resend for email delivery
- Rate-limited mail delivery system to prevent API throttling

## Related Issues

- Linear Issue: UNL-99
- Related to UNL-54 (email notification stability improvements)