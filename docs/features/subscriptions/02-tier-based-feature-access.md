# Tier-Based Feature Access Control with ActionPolicy

**Status**: 📋 Proposed Solution  
**Rails Version**: 7.0.8+  
**Complexity**: 2 points  
**Reversibility**: ✅ High - Single line change to revert

## Overview

This document outlines the simplest, most maintainable approach to restrict features (starting with the Want model) to specific subscription tiers using ActionPolicy. The solution leverages ActionPolicy's policy inheritance pattern to create a clean, declarative, and universally reusable system for tier-based access control.

## Core Architecture

### The Pattern: Tier-Specific Base Policies

Instead of embedding tier-checking logic into every feature policy, we create tier-specific base policies that inherit from `ApplicationPolicy`. Feature policies then inherit from the appropriate tier policy rather than directly from `ApplicationPolicy`.

```
ApplicationPolicy (base authentication)
    ↓
PilotPolicy (tier restriction)
    ↓
WantPolicy (feature-specific rules)
```

## Implementation Steps

### Step 1: Strengthen ApplicationPolicy

First, ensure `ApplicationPolicy` provides baseline authentication:

```ruby
# app/policies/application_policy.rb
class ApplicationPolicy < ActionPolicy::Base
  # Existing premium/pilot checks remain
  def standard_feature_access?
    user.present? && user.active_subscription?
  end

  def pilot_feature_access?
    user.present? && user.tier_pilot?
  end
  
  # Add new pilot check
  def pilot_feature_access?
    user.present? && user.tier_pilot?
  end
end
```

### Step 2: Create PilotPolicy Base Class

Create a new base policy for Pilot-only features:

```ruby
# app/policies/pilot_policy.rb
class PilotPolicy < ApplicationPolicy
  # Use ActionPolicy's pre_check to enforce tier requirement
  # This runs before any specific rule in child policies
  pre_check :require_pilot_tier

  private

  def require_pilot_tier
    deny! unless pilot_feature_access?
  end
end
```

### Step 3: Update WantPolicy

Change WantPolicy to inherit from PilotPolicy:

```ruby
# app/policies/want_policy.rb
class WantPolicy < PilotPolicy  # Changed from ApplicationPolicy
  # All existing rules remain unchanged
  def index?
    true # Pilot check already handled by parent
  end

  def show_my?
    user.present?
  end

  def show?
    true
  end

  def create?
    user.present?
  end

  def new?
    create?
  end

  def edit?
    owner?
  end

  def update?
    owner?
  end

  def destroy?
    owner?
  end

  private

  def owner?
    user == record.user
  end
end
```

### Step 4: Handle Authorization Failures Gracefully

Update ApplicationController to provide meaningful feedback:

```ruby
# app/controllers/application_controller.rb
class ApplicationController < ActionController::Base
  # Add to existing rescue_from or create new one
  rescue_from ActionPolicy::Unauthorized do |exception|
    # Check if it's a tier restriction
    if exception.result&.reasons&.details&.include?(:require_pilot_tier)
      message = t('subscriptions.pilot_required',
                  default: 'This feature requires a Pilot subscription.')
    else
      message = t('common.unauthorized', 
                  default: 'You are not authorized to perform this action.')
    end
    
    respond_to do |format|
      format.html { redirect_back(fallback_location: root_path, alert: message) }
      format.json { render json: { error: message }, status: :forbidden }
    end
  end
end
```

### Step 5: Update UI to Hide Inaccessible Features

In views, use ActionPolicy's `allowed_to?` helper to conditionally show UI elements:

```erb
<!-- app/views/layouts/_navigation.html.erb -->
<% if allowed_to?(:index?, Want) %>
  <%= link_to t('wants.navigation.my_wants'), show_my_wants_path, 
              class: 'nav-link' %>
<% end %>

<!-- app/views/projects/show.html.erb -->
<% if allowed_to?(:create?, Want.new) %>
  <%= link_to t('wants.create.button'), new_want_path, 
              class: 'btn btn-primary' %>
<% end %>
```

## Creating Additional Tier-Restricted Features

### For Premium Tier Features

Create a PremiumPolicy base class:

```ruby
# app/policies/premium_policy.rb
class PremiumPolicy < ApplicationPolicy
  pre_check :require_premium_tier
  
  private
  
  def require_premium_tier
    # Premium and Pilot users have access
    deny! unless standard_feature_access?
  end
end
```

Then any feature policy can inherit from it:

```ruby
class AdvancedSearchPolicy < PremiumPolicy
  # Feature-specific rules
end
```

### For Free Tier (All Authenticated Users)

Simply inherit from ApplicationPolicy directly:

```ruby
class BasicFeaturePolicy < ApplicationPolicy
  # Open to all authenticated users
end
```

## Advantages of This Approach

### 1. **Simplicity**
- Single inheritance change to restrict a feature
- No complex feature flag systems
- No database changes required

### 2. **Maintainability**
- Tier requirements are self-documenting in the class definition
- All tier logic centralized in base policies
- Easy to understand at a glance

### 3. **Reversibility**
- To open Want to all users: Change `class WantPolicy < PilotPolicy` back to `class WantPolicy < ApplicationPolicy`
- One-line change with zero side effects

### 4. **Scalability**
- Pattern works for any number of tiers
- Easy to add new tier-restricted features
- Supports complex tier hierarchies (e.g., Premium includes all Free features)

### 5. **Rails/ActionPolicy Best Practices**
- Uses ActionPolicy's built-in `pre_check` functionality
- Leverages policy inheritance
- Maintains clean separation of concerns
- No external dependencies

## Testing Strategy

### Policy Tests

```ruby
# spec/policies/want_policy_spec.rb
RSpec.describe WantPolicy do
  let(:user) { build(:user, subscription_tier: tier) }
  let(:want) { build(:want) }
  let(:policy) { described_class.new(want, user: user) }
  
  describe "tier access" do
    context "with pilot tier" do
      let(:tier) { :pilot }
      
      it "allows all actions" do
        expect(policy.apply(:index?)).to be true
        expect(policy.apply(:create?)).to be true
      end
    end
    
    context "with premium tier" do
      let(:tier) { :premium }
      
      it "denies all actions" do
        expect(policy.apply(:index?)).to be false
        expect(policy.apply(:create?)).to be false
      end
    end
    
    context "with free tier" do
      let(:tier) { :free }
      
      it "denies all actions" do
        expect(policy.apply(:index?)).to be false
        expect(policy.apply(:create?)).to be false
      end
    end
  end
end
```

### Controller Tests

```ruby
# spec/requests/wants_spec.rb
RSpec.describe "Wants", type: :request do
  describe "GET /wants" do
    context "as pilot user" do
      let(:user) { create(:user, :pilot) }
      
      before { sign_in user }
      
      it "allows access" do
        get wants_path
        expect(response).to have_http_status(:success)
      end
    end
    
    context "as non-pilot user" do
      let(:user) { create(:user, :premium) }
      
      before { sign_in user }
      
      it "redirects with alert" do
        get wants_path
        expect(response).to redirect_to(root_path)
        expect(flash[:alert]).to include("Pilot subscription")
      end
    end
  end
end
```

## Migration Path

### Phase 1: Implement for Want Model (Current)
1. Create PilotPolicy
2. Update WantPolicy to inherit from PilotPolicy
3. Update UI to hide Want features from non-Pilot users
4. Deploy and monitor

### Phase 2: Extend to Other Features
As new features need tier restrictions:
1. Create appropriate tier base policy if needed
2. Update feature policy to inherit from tier policy
3. Test thoroughly

### Phase 3: Future Flexibility
When Want feature opens to other tiers:
1. Option A: Change WantPolicy back to inherit from ApplicationPolicy (fully open)
2. Option B: Change WantPolicy to inherit from PremiumPolicy (premium + pilot)
3. Option C: Add conditional logic within WantPolicy for gradual rollout

## Alternative Approaches Considered

### 1. **Feature Flags (Rejected)**
- **Pros**: Very flexible, gradual rollouts
- **Cons**: Adds complexity, requires additional gems or infrastructure
- **Verdict**: Overkill for simple tier-based access

### 2. **Inline Tier Checks (Rejected)**
```ruby
def create?
  user.tier_pilot? && user.present?
end
```
- **Pros**: Explicit, no abstraction
- **Cons**: Repetitive, harder to change globally, violates DRY
- **Verdict**: Doesn't scale well

### 3. **Module Mixins (Rejected)**
```ruby
module PilotRequired
  extend ActiveSupport::Concern
  # ...
end
```
- **Pros**: Reusable, composable
- **Cons**: More complex, less idiomatic for ActionPolicy
- **Verdict**: Inheritance is cleaner for this use case

## Rollback Plan

If the tier restriction needs to be removed urgently:

1. **Immediate**: Change WantPolicy inheritance back to ApplicationPolicy
2. **Deploy**: Single file change, minimal risk
3. **Verify**: Check that all users can access Want features
4. **Clean up**: Remove PilotPolicy if no longer needed

## Monitoring & Success Metrics

- Track Pilot tier conversion rate after Want restriction
- Monitor unauthorized access attempts (indicates user interest)
- Track support tickets about feature access
- Measure page views of upgrade prompt from Want access attempts

## Conclusion

This approach provides the simplest possible solution using ActionPolicy's core functionality. It requires minimal code changes, maintains clean separation of concerns, and can be easily extended or reversed as business requirements evolve. The pattern scales naturally to any number of tiers and features while keeping the codebase maintainable and self-documenting.

## Commands Reference

```bash
# Generate new tier-restricted feature policy
rails g action_policy:policy NewFeature
# Then manually change parent class to appropriate tier policy

# Run policy tests
bundle exec rspec spec/policies/want_policy_spec.rb

# Test in console
user = User.find_by(email: "<EMAIL>")
want = Want.first
WantPolicy.new(want, user: user).apply(:create?)
```