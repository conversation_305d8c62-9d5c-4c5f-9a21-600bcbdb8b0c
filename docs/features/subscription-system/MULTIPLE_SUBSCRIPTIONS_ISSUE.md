# Multiple Subscriptions Issue Analysis

## Current Behavior

The system currently uses `has_one :active_subscription` which means:
- Only ONE subscription is considered "active" at a time
- It returns the FIRST active/trialing subscription found
- If a user has multiple active subscriptions, only the first one is used

## Problem Scenarios

### Scenario 1: Admin Manual + Stripe Subscription
1. User has active Stripe Premium subscription 
2. <PERSON><PERSON> manually creates Pilot subscription for the same user
3. System will use whichever subscription was created FIRST (based on ID order)
4. This could mean the user gets LESS access than intended

### Scenario 2: Multiple Stripe Subscriptions  
- User somehow ends up with multiple active Stripe subscriptions
- Only the first one is considered for authorization

## Current Authorization Logic

```ruby
# ApplicationPolicy
def standard_feature_access?
  user.present? && user.active_subscription?
end

def pilot_feature_access?
  user.present? && user.tier_pilot?  # <-- Uses legacy field!
end
```

**Critical Issue**: `pilot_feature_access?` still uses the legacy `tier_pilot?` method which checks the old `subscription_tier` enum field, NOT the new subscription model!

## Solutions

### Option 1: Prevent Multiple Active Subscriptions (RECOMMENDED)
- When creating a new subscription, automatically cancel any existing active ones
- This is already partially implemented in `create_subscription` method
- Need to enforce this in ALL subscription creation paths

### Option 2: Use Highest Tier
- Change `active_subscription` to return the subscription with the highest tier
- Order by plan tier: pilot > premium > free
- This ensures users always get maximum access

### Option 3: Support Multiple Subscriptions
- Change to `has_many :active_subscriptions`
- Check all active subscriptions for authorization
- More complex but supports legitimate use cases

## Immediate Fix Needed

1. **Fix pilot authorization** - Update `pilot_feature_access?` to use subscription model:
```ruby
def pilot_feature_access?
  user.present? && user.active_subscription&.plan&.pilot?
end
```

2. **Enforce single active subscription** - Cancel existing when creating new:
```ruby
# Already in admin controller, need in StripeWebhookJob too
if @user.active_subscription.present?
  @user.active_subscription.cancel!(reason: "New subscription created")
end
```

## Recommendation

1. **Short term**: Fix the authorization methods to use subscription model
2. **Medium term**: Enforce single active subscription rule
3. **Long term**: Consider if multiple subscriptions are a valid business case