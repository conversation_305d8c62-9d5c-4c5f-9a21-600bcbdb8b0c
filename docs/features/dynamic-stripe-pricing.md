# Dynamic Stripe Pricing Implementation

## Overview

The subscription system dynamically fetches prices from Stripe based on product metadata, eliminating the need for hardcoded prices in environment variables or credentials.

## How It Works

1. **Product Discovery**: The system searches for all active Stripe products
2. **Premium Product Identification**: Products are identified as "Premium" if they meet ANY of these criteria:
   - Product name contains "premium" (case-insensitive)
   - Product has metadata `tier: premium`
   - Product has metadata `subscription_tier: premium`
3. **Price Fetching**: For each Premium product found:
   - Monthly price: Looks for a price with `interval: month`
   - Annual price: Looks for a price with `interval: year`
4. **Caching**: Results are cached for 1 hour to minimize API calls

## Setting Up Stripe Products

### Option 1: Using Product Name
Create products in Stripe Dashboard with "Premium" in the name:
- "Premium Monthly Subscription"
- "Premium Annual Subscription"

### Option 2: Using Metadata
Add metadata to your Stripe products:
```
tier: premium
```
or
```
subscription_tier: premium
```

### Required Price Configuration
Each premium product needs at least one price with:
- `recurring.interval: month` for monthly pricing
- `recurring.interval: year` for annual pricing

## Test Products Provided

For testing, these product IDs were mentioned:
- Monthly (€19): `prod_SypeNN9Hbf2ZYL`
- Annual (€190): `prod_SzERHPv4MpeVwB`

However, the system will dynamically find these if they:
1. Have "Premium" in their names
2. Have the appropriate metadata set
3. Have recurring prices configured

## Cache Management

To clear the price cache:
```ruby
# In Rails console
ApplicationController.helpers.clear_stripe_price_cache
```

## Troubleshooting

If prices aren't showing:

1. **Check Stripe Products**:
   - Ensure products are active
   - Verify product name contains "Premium" OR has correct metadata
   - Check that prices are configured as recurring (monthly/yearly)

2. **Check Stripe Configuration**:
   - Verify `STRIPE_MODE` environment variable (test/live)
   - Ensure Stripe API keys are configured in credentials
   - Check Rails logs for Stripe API errors

3. **Clear Cache**:
   ```bash
   rails cache:clear
   ```

## Implementation Files

- `app/helpers/subscription_helper.rb`: Dynamic price fetching logic
- `app/controllers/subscriptions_controller.rb`: Uses helper to get prices
- `app/views/subscriptions/index.html.erb`: Displays prices and handles toggle
- `app/services/stripe_subscription_service.rb`: Handles checkout with dynamic price IDs