# Background Jobs Implementation Guide

This document provides comprehensive guidance for the background jobs system implemented using Rails Active Jobs with the GoodJob adapter.

## Overview

The application uses **GoodJob** as the background job processing framework, leveraging PostgreSQL for job storage and management. This setup provides reliable, persistent job queuing with excellent monitoring capabilities.

## Architecture

### Job Processing Flow
1. **Job Enqueueing**: Jobs are queued using Rails Active Job with `deliver_later`
2. **Database Storage**: Jobs are stored in PostgreSQL tables managed by GoodJob
3. **Worker Processing**: Separate worker processes poll the database and execute jobs
4. **Monitoring**: Admin dashboard provides real-time job monitoring and management

### Technology Stack
- **Active Job**: Rails' built-in job framework for job abstraction
- **GoodJob**: PostgreSQL-based job queue adapter with advanced features
- **PostgreSQL**: Database storage for job persistence and reliability
- **Render Workers**: Dedicated worker processes on Render platform

## Configuration

### Gem Dependencies
```ruby
# Gemfile
gem 'good_job'  # Background job processing
```

### Active Job Configuration
```ruby
# config/application.rb
config.active_job.queue_adapter = :good_job
```

### Database Tables
GoodJob creates several tables for job management:
- `good_jobs` - Main job storage with execution state
- `good_job_batches` - Batch job coordination
- `good_job_executions` - Execution history and debugging
- `good_job_processes` - Worker process tracking
- `good_job_settings` - Configuration storage

## Deployment Configuration

### Render Platform Setup
The application is configured for deployment on Render with separate web and worker services:

```yaml
# render.yaml
services:
  # Web service - handles HTTP requests
  - type: web
    name: unlisters-app
    env: ruby
    buildCommand: bundle install && npm install && npm run build
    startCommand: bundle exec puma -C config/puma.rb

  # Worker service - processes background jobs
  - type: worker
    name: unlisters-app-worker
    env: ruby
    buildCommand: ./bin/worker-build.sh
    startCommand: bundle exec good_job start
```

### Environment Variables
Both services require access to:
- `RAILS_ENV=production`
- `DATABASE_URL` (shared from database service)
- `RAILS_MASTER_KEY` (for credentials decryption)

## Monitoring & Administration

### GoodJob Dashboard
- **URL**: `/good_job` (admin access only)
- **Authentication**: Requires user with `admin` or `super_boss` role
- **Features**: 
  - Real-time job status monitoring
  - Job execution history and performance metrics
  - Failed job debugging and retry capabilities
  - Worker process status and health checks

### Access Control
```ruby
# config/routes.rb
mount GoodJob::Engine => 'good_job', :constraints => lambda { |request|
  request.env['warden'].authenticate? && request.env['warden'].user.admin?
}
```

### Security Features
- Dashboard accessible only to authenticated admin users
- No public access to job monitoring interface
- Leverages existing Devise authentication with role-based authorization

## Job Types & Usage

### Email Jobs (Primary Use Case)
The main use case for background jobs in this application is asynchronous email sending:

```ruby
# Synchronous (blocking) - NOT recommended for production
NotificationMailer.access_request_notification(user).deliver_now

# Asynchronous (background) - RECOMMENDED
NotificationMailer.access_request_notification(user).deliver_later
```

### Current Mailer Types
- `NotificationMailer` - User notifications, connection requests, project approvals
- `DeviseMailer` - Authentication emails (invitations, confirmations, password resets)

### Rate Limiting and Reliability (Updated September 2025)

#### Critical Improvements
The email notification system has been enhanced to prevent race conditions and ensure 100% delivery reliability:

1. **Single-threaded mailer queue** - Eliminates race conditions in rate limiting
   ```ruby
   config.good_job.queues = 'mailers:1;default:3;*:5'  # Single thread for mailers
   ```

2. **Conservative rate limiting** - 1 email per 1.5 seconds (well below Resend's 2/sec limit)
   ```ruby
   good_job_control_concurrency_with(
     perform_throttle: [1, 1.5.seconds],
     key: 'resend_api_rate_limit',
     total_limit: 1
   )
   ```

3. **Idempotent delivery with tracking** - Prevents duplicate emails
   ```ruby
   # EmailDeliveryTracker ensures each email is sent only once
   tracker = EmailDeliveryTracker.find_or_create_by(
     project: project,
     user: user,
     email_type: 'new_project_notification'
   )
   next if tracker.sent?  # Skip if already sent
   ```

4. **Automated health monitoring** - Hourly checks for delivery issues
   ```ruby
   # EmailHealthCheckJob runs every hour
   config.good_job.cron['email_health_check'] = {
     cron: '0 * * * *',
     class: 'EmailHealthCheckJob'
   }
   ```

#### Bulk Notification Pattern
The `BulkNotificationJob` now properly respects rate limiting:
```ruby
def perform(project, user_ids)
  User.where(id: user_ids).find_each do |user|
    # Create tracker for idempotency
    tracker = EmailDeliveryTracker.find_or_create_by(...)
    next if tracker.sent?
    
    # Queue email with rate limiting (uses deliver_later, not deliver_now)
    NotificationMailer.new_project_notification(project, user).deliver_later
    tracker.mark_as_queued!
  end
end
```

#### Monitoring Email Delivery
Access delivery statistics through:
- GoodJob dashboard at `/good_job` for job queue status
- `EmailDeliveryTracker` records for delivery audit trail
- Automated alerts when failures exceed thresholds (>10 failed, >20 stuck)

## Best Practices

### Job Design
1. **Idempotent Jobs**: Design jobs to be safely retryable
2. **Small Payloads**: Keep job arguments minimal and serializable
3. **Error Handling**: Implement proper exception handling and logging
4. **Timeouts**: Set appropriate job timeouts for long-running tasks

### Performance Optimization
1. **Queue Prioritization**: Use different queues for different job types
2. **Batch Processing**: Group related jobs for efficiency
3. **Worker Scaling**: Monitor job queue depth and scale workers accordingly

### Monitoring
1. **Regular Dashboard Checks**: Monitor job failure rates and execution times
2. **Alert Setup**: Configure alerts for high failure rates or queue backlog
3. **Performance Metrics**: Track job throughput and worker utilization

## Development Workflow

### Local Development
```bash
# Start Rails server (includes job processing in development)
bin/dev

# Or manually start job worker (if needed)
bundle exec good_job start
```

### Testing Jobs
```ruby
# In RSpec tests
it "enqueues notification email" do
  expect {
    NotificationMailer.access_request_notification(user).deliver_later
  }.to have_enqueued_job(ActionMailer::MailDeliveryJob)
end
```

### Debugging Failed Jobs
1. Access GoodJob dashboard at `/good_job`
2. Navigate to "Failed" jobs section
3. Review error messages and stack traces
4. Retry jobs after fixing underlying issues

## Migration from Synchronous Processing

### Converting Existing Mailers
Replace all instances of `deliver_now` with `deliver_later`:

```ruby
# Before (synchronous)
NotificationMailer.access_request_notification(user).deliver_now

# After (asynchronous)
NotificationMailer.access_request_notification(user).deliver_later
```

### Gradual Migration Strategy
1. **Start with non-critical emails** (notifications, marketing)
2. **Monitor job success rates** and performance
3. **Migrate critical emails** (authentication, security) after validation
4. **Remove synchronous fallbacks** once system is stable

## Troubleshooting

### Common Issues

#### Worker Not Processing Jobs
- **Check**: Worker service is running on Render
- **Check**: Database connectivity from worker service
- **Check**: Environment variables are properly configured

#### Jobs Failing Consistently
- **Check**: Job arguments are properly serializable
- **Check**: External service dependencies (email providers, APIs)
- **Check**: Database connection limits and timeouts

#### High Memory Usage
- **Monitor**: Job payload sizes and worker memory consumption
- **Consider**: Job batching or payload optimization
- **Check**: Memory leaks in job code

### Monitoring Commands
```bash
# Check job queue status (in Rails console)
GoodJob.check_status

# Force job processing (development only)
GoodJob.perform_inline = true
```

## Future Enhancements

### Potential Job Types
1. **File Processing**: Image resizing, document conversion
2. **Data Exports**: CSV generation, report creation
3. **Geocoding**: Batch location processing for projects
4. **Cleanup Tasks**: Orphaned file removal, data archival
5. **Notifications**: Push notifications, webhook deliveries

### Advanced Features
1. **Scheduled Jobs**: Periodic tasks using cron-like scheduling
2. **Job Prioritization**: Priority queues for urgent vs. normal tasks
3. **Retry Strategies**: Custom retry logic for different failure types
4. **Job Batching**: Coordinated processing of related jobs

## Security Considerations

### Job Data Protection
- **Sensitive Data**: Avoid storing sensitive information in job arguments
- **Encryption**: Use Rails encrypted attributes for sensitive job payloads
- **Access Control**: Ensure job processing respects user permissions

### Dashboard Security
- **Admin Only**: Dashboard access restricted to admin users
- **HTTPS Only**: Ensure dashboard is only accessible over HTTPS in production
- **Audit Logging**: Consider logging dashboard access for security auditing

---

*Last Updated: June 2025*
*Implementation Date: June 9, 2025*