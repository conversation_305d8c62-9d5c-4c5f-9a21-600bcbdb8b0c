#!/usr/bin/env ruby
# ABOUTME: Debug script to test reCAPTCHA verification logic
# ABOUTME: Simulates the verification process to identify issues

require 'net/http'
require 'json'

def test_recaptcha_verification(token, secret_key)
  puts "\n=== Testing reCAPTCHA Verification ==="
  puts "Token present: #{!token.to_s.strip.empty?}"
  puts "Secret key present: #{!secret_key.to_s.strip.empty?}"
  
  if token.to_s.strip.empty?
    puts "❌ ERROR: Token is blank"
    return false
  end
  
  if secret_key.to_s.strip.empty?
    puts "❌ ERROR: Secret key is blank"
    return false
  end
  
  puts "\nMaking request to Google reCAPTCHA API..."
  
  uri = URI('https://www.google.com/recaptcha/api/siteverify')
  response = Net::HTTP.post_form(uri, {
    'secret' => secret_key,
    'response' => token,
    'remoteip' => '**************' # Example IP from logs
  })
  
  puts "Response status: #{response.code}"
  puts "Response body: #{response.body}"
  
  result = JSON.parse(response.body)
  
  puts "\n=== Parsed Result ==="
  puts "Success: #{result['success']}"
  puts "Score: #{result['score']}" if result['score']
  puts "Action: #{result['action']}" if result['action']
  puts "Hostname: #{result['hostname']}" if result['hostname']
  puts "Error codes: #{result['error-codes']}" if result['error-codes']
  
  if result['success']
    if result['score']
      score_check = result['score'].to_f >= 0.3
      puts "Score check (>= 0.3): #{score_check}"
      return score_check
    else
      puts "⚠️  WARNING: No score in response (might not be v3)"
      return false
    end
  else
    puts "❌ Verification failed"
    return false
  end
  
rescue => e
  puts "❌ Exception: #{e.message}"
  puts e.backtrace.first(5)
  false
end

# Test with a dummy token (this will fail but show the error)
puts "Testing with dummy token (expected to fail)..."
test_recaptcha_verification("dummy_token", "dummy_secret")

puts "\n" + "="*50
puts "To test with real credentials, you need:"
puts "1. The actual reCAPTCHA secret key from production"
puts "2. A valid token generated from the frontend"
puts "="*50