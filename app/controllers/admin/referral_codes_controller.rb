# ABOUTME: Admin controller for managing referral codes and tracking usage
# ABOUTME: Allows admins to create, view, and manage referral codes for subscription tier upgrades
class Admin::ReferralCodesController < Admin::BaseController
  before_action :set_referral_code, only: [:show, :edit, :update, :destroy]
  
  def index
    codes_query = ReferralCode.includes(:created_by).order(created_at: :desc)
    @pagy, @codes = pagy(codes_query, limit: 20)
    @new_code = ReferralCode.new
  end
  
  def show
  end
  
  def edit
  end
  
  def create
    @new_code = ReferralCode.new(code_params.merge(created_by: current_user))
    
    if @new_code.save
      redirect_to admin_referral_codes_path, notice: "Referral code created successfully"
    else
      codes_query = ReferralCode.includes(:created_by).order(created_at: :desc)
      @pagy, @codes = pagy(codes_query, limit: 20)
      render :index
    end
  end
  
  def update
    if @code.update(code_params)
      redirect_to admin_referral_codes_path, notice: "Referral code updated successfully"
    else
      render :show
    end
  end
  
  def destroy
    @code.destroy
    redirect_to admin_referral_codes_path, notice: "Referral code deleted successfully"
  end
  
  private
  
  def set_referral_code
    @code = ReferralCode.find(params[:id])
  end
  
  def code_params
    params.require(:referral_code).permit(:code, :tier_upgrade_to, :duration_months, :max_uses, :expires_at, :description, :discount_percentage, :status)
  end
end