# ABOUTME: Admin controller for managing user subscriptions and tier assignments
# ABOUTME: Provides admin interface for viewing users by tier and manually updating subscription status
class Admin::SubscriptionsController < Admin::BaseController

  def index
    # Filter users based on status parameter
    @status_filter = params[:status]

    @users_query = case @status_filter
                   when 'free'
                     # Users with active free plan subscription or legacy free tier
                     User.left_joins(:active_subscription => :plan)
                          .where("plans_subscriptions.tier = ? OR (users.subscription_tier = ? AND plans_subscriptions.id IS NULL)", 
                                 Plan.tiers['free'], User.subscription_tiers['free'])
                   when 'standard'
                     # Users with active standard plan subscription or legacy standard tier
                     User.left_joins(:active_subscription => :plan)
                          .where("plans_subscriptions.tier = ? OR (users.subscription_tier = ? AND plans_subscriptions.id IS NULL)", 
                                 Plan.tiers['standard'], User.subscription_tiers['standard'])
                   when 'pilot'
                     # Users with active pilot plan subscription or legacy pilot tier
                     User.left_joins(:active_subscription => :plan)
                          .where("plans_subscriptions.tier = ? OR (users.subscription_tier = ? AND plans_subscriptions.id IS NULL)", 
                                 Plan.tiers['pilot'], User.subscription_tiers['pilot'])
                   when 'approved'
                     User.approved
                   when 'pending'
                     User.where(approved: false)
                   when 'active_subscriptions'
                     User.joins(:active_subscription)
                   when 'expired_subscriptions'
                     User.joins(:subscriptions).where(subscriptions: { status: [:canceled, :unpaid, :incomplete_expired] })
                          .distinct
                   else
                     User.all
                   end

    @users_query = @users_query.includes(:user_profile, :active_subscription => :plan).order(created_at: :desc, id: :desc)
    @pagy, @users = pagy(@users_query, limit: 25)

    # Get counts for status summary
    @status_counts = {
      total: User.count,
      free: User.left_joins(:active_subscription => :plan)
                .where("plans_subscriptions.tier = ? OR (users.subscription_tier = ? AND plans_subscriptions.id IS NULL)", 
                       Plan.tiers['free'], User.subscription_tiers['free']).count,
      standard: User.left_joins(:active_subscription => :plan)
                   .where("plans_subscriptions.tier = ? OR (users.subscription_tier = ? AND plans_subscriptions.id IS NULL)", 
                          Plan.tiers['standard'], User.subscription_tiers['standard']).count,
      pilot: User.left_joins(:active_subscription => :plan)
                 .where("plans_subscriptions.tier = ? OR (users.subscription_tier = ? AND plans_subscriptions.id IS NULL)", 
                        Plan.tiers['pilot'], User.subscription_tiers['pilot']).count,
      approved: User.approved.count,
      pending: User.where(approved: false).count,
      active_subscriptions: User.joins(:active_subscription).distinct.count,
      expired_subscriptions: User.joins(:subscriptions)
                                 .where(subscriptions: { status: [:canceled, :unpaid, :incomplete_expired] })
                                 .distinct.count
    }

    # Load available plans for the subscription form
    @available_plans = Plan.active.order(:tier, :interval)
  end
  
  def show
    @user = User.find(params[:id])
    @subscriptions = @user.subscriptions.includes(:plan).order(created_at: :desc)
    @available_plans = Plan.active.order(:tier, :interval)
  end
  
  def sync_from_stripe
    @user = User.find(params[:id])
    
    unless @user.stripe_customer_id.present?
      redirect_to admin_subscription_path(@user), 
                  alert: "User has no Stripe Customer ID - cannot sync"
      return
    end
    
    begin
      # Fetch all subscriptions from Stripe for this customer
      stripe_subscriptions = Stripe::Subscription.list(
        customer: @user.stripe_customer_id,
        status: 'all',
        limit: 100
      )
      
      synced_count = 0
      updated_count = 0
      
      stripe_subscriptions.data.each do |stripe_sub|
        # Find or initialize local subscription
        subscription = @user.subscriptions.find_or_initialize_by(
          stripe_subscription_id: stripe_sub.id
        )
        
        # Find or create plan
        price_data = stripe_sub.items.data.first.price
        plan = Plan.find_by(stripe_price_id: price_data.id)
        
        unless plan
          # Fetch product data from Stripe
          product = Stripe::Product.retrieve(price_data.product)
          
          # Try to find existing plan by name first, then create or update
          plan = Plan.find_or_initialize_by(stripe_price_id: price_data.id)
          
          # If this is a new plan but the name already exists, make it unique
          base_name = product.name
          if plan.new_record? && Plan.exists?(name: base_name)
            # Append the Stripe price ID to make it unique
            unique_name = "#{base_name} (#{price_data.id})"
            counter = 1
            while Plan.exists?(name: unique_name)
              unique_name = "#{base_name} (#{price_data.id}_#{counter})"
              counter += 1
            end
            plan.name = unique_name
          else
            plan.name = base_name
          end
          
          # Update all plan attributes
          plan.assign_attributes(
            stripe_product_id: product.id,
            price_cents: price_data.unit_amount,
            interval: price_data.recurring ? price_data.recurring.interval : 'one_time',
            tier: determine_tier_from_product(product),
            metadata: product.metadata || {},
            active: product.active
          )
          
          plan.save!
        end
        
        # Update subscription attributes
        was_new = subscription.new_record?
        
        subscription.update!(
          plan: plan,
          stripe_customer_id: stripe_sub.customer,
          stripe_status: stripe_sub.status,
          status: Subscription.map_stripe_status(stripe_sub.status, stripe_sub.cancel_at_period_end),
          current_period_start: stripe_sub.current_period_start ? 
            Time.at(stripe_sub.current_period_start) : Time.current,
          current_period_end: stripe_sub.current_period_end ? 
            Time.at(stripe_sub.current_period_end) : (plan.interval == 'year' ? 1.year.from_now : 1.month.from_now),
          trial_start: stripe_sub.trial_start ? Time.at(stripe_sub.trial_start) : nil,
          trial_end: stripe_sub.trial_end ? Time.at(stripe_sub.trial_end) : nil,
          cancel_at_period_end: stripe_sub.cancel_at_period_end || false,
          canceled_at: stripe_sub.canceled_at ? Time.at(stripe_sub.canceled_at) : nil,
          payment_provider: :stripe,
          provider_data: stripe_sub.to_h
        )
        
        if was_new
          synced_count += 1
        else
          updated_count += 1
        end
      end
      
      message = "Successfully synced from Stripe: "
      message += "#{synced_count} new subscriptions created" if synced_count > 0
      message += ", " if synced_count > 0 && updated_count > 0
      message += "#{updated_count} subscriptions updated" if updated_count > 0
      message = "No subscriptions found in Stripe" if synced_count == 0 && updated_count == 0
      
      redirect_to admin_subscription_path(@user), notice: message
      
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe sync error: #{e.message}"
      redirect_to admin_subscription_path(@user), 
                  alert: "Failed to sync from Stripe: #{e.message}"
    end
  end
  
  def create_subscription
    @user = User.find(params[:id])
    @plan = Plan.find(params[:plan_id])
    
    # Cancel any active subscription first
    if @user.active_subscription.present?
      @user.active_subscription.cancel!(reason: "Admin created new subscription")
    end
    
    # Create new subscription
    subscription = @user.subscriptions.build(
      plan: @plan,
      status: :active,
      payment_provider: :manual,
      current_period_start: Time.current,
      current_period_end: calculate_period_end(@plan)
    )
    
    if subscription.save
      redirect_to admin_subscription_path(@user), 
                  notice: "Subscription created successfully for #{@user.email}"
    else
      redirect_to admin_subscription_path(@user), 
                  alert: "Failed to create subscription: #{subscription.errors.full_messages.join(', ')}"
    end
  end
  
  def cancel_subscription
    @user = User.find(params[:id])
    @subscription = @user.subscriptions.find(params[:subscription_id])
    
    begin
      # IMPORTANT: Prevent admins from canceling live Stripe subscriptions from this panel.
      if @subscription.stripe_subscription_id.present?
        redirect_to admin_subscription_path(@user), 
                    alert: "Stripe subscriptions cannot be canceled from the admin panel. Please use the Stripe dashboard."
        return
      end

      # For internal/manual subscriptions, cancel immediately.
      @subscription.cancel!(
        reason: params[:cancel_reason] || "Admin canceled",
        at_period_end: false
      )
      
      redirect_to admin_subscription_path(@user), 
                  notice: "Internal subscription canceled successfully."

    rescue ActiveRecord::RecordInvalid => e
      Rails.logger.error "Failed to cancel subscription for user #{@user.id} - validation error: #{e.message}"
      redirect_to admin_subscription_path(@user), 
                  alert: "Failed to cancel subscription: Invalid subscription data"
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error "Subscription not found for cancellation - user #{@user.id}: #{e.message}"
      redirect_to admin_subscription_path(@user), 
                  alert: "Subscription not found"
    rescue ActiveRecord::ActiveRecordError => e
      Rails.logger.error "Database error canceling subscription for user #{@user.id}: #{e.message}"
      redirect_to admin_subscription_path(@user), 
                  alert: "Failed to cancel subscription due to database error"
    end
  end
  
  def reactivate_subscription
    @user = User.find(params[:id])
    @subscription = @user.subscriptions.find(params[:subscription_id])
    
    if @subscription.reactivate!
      redirect_to admin_subscription_path(@user), 
                  notice: "Subscription reactivated successfully"
    else
      redirect_to admin_subscription_path(@user), 
                  alert: "Cannot reactivate this subscription"
    end
  end
  
  def update_tier
    @user = User.find(params[:id])
    
    # Legacy support - update user tier attributes
    if @user.update(tier_params)
      # Also create a subscription record if using new system
      if params[:create_subscription] == 'true' && params[:plan_id].present?
        plan = Plan.find(params[:plan_id])
        create_subscription_for_user(@user, plan)
      end
      
      redirect_to admin_subscriptions_path, notice: "Tier updated successfully for #{@user.email}"
    else
      redirect_to admin_subscriptions_path, alert: "Failed to update tier: #{@user.errors.full_messages.join(', ')}"
    end
  end
  
  def update_approval
    @user = User.find(params[:id])
    action = params[:approval_action] || params[:user]&.dig(:approval_action)
    
    begin
      case action
      when 'approve'
        @user.approve!
        redirect_to admin_subscriptions_path, notice: "User #{@user.email} has been approved successfully."
      when 'disapprove'
        @user.disapprove!
        redirect_to admin_subscriptions_path, notice: "User #{@user.email} has been disapproved."
      else
        redirect_to admin_subscriptions_path, alert: "Invalid approval action."
      end
    rescue => e
      redirect_to admin_subscriptions_path, alert: "Failed to update approval status: #{e.message}"
    end
  end

  def destroy
    @user = User.find(params[:id])
    
    # Prevent admin from deleting themselves
    if @user == current_user
      redirect_to admin_subscriptions_path, alert: "Cannot delete your own account"
      return
    end
    
    # Require confirmation
    required_confirmation = "DELETE #{@user.email}"
    provided_confirmation = params[:confirm_deletion]
    
    if provided_confirmation.blank?
      redirect_to admin_subscriptions_path, alert: "Deletion cancelled - confirmation required"
      return
    end
    
    if provided_confirmation != required_confirmation
      redirect_to admin_subscriptions_path, alert: "Confirmation text does not match. Expected: '#{required_confirmation}'"
      return
    end
    
    begin
      user_email = @user.email
      @user.destroy!
      redirect_to admin_subscriptions_path, notice: "User #{user_email} has been deleted successfully along with all associated data (projects, wants, connections, profile)"
    rescue => e
      redirect_to admin_subscriptions_path, alert: "Failed to delete user: #{e.message}"
    end
  end
  
  private
  
  def tier_params
    params.permit(:subscription_tier, :subscription_expires_at)
  end
  
  def calculate_period_end(plan)
    case plan.interval
    when 'month'
      1.month.from_now
    when 'year'
      1.year.from_now
    when 'one_time'
      100.years.from_now
    else
      1.month.from_now
    end
  end
  
  def determine_tier_from_product(product)
    # Determine tier based on product name or metadata
    name = product.name.downcase
    metadata = product.metadata || {}
    
    if metadata['tier']
      metadata['tier']
    elsif name.include?('pilot')
      'pilot'
    elsif name.include?('standard') 
      'standard'
    elsif name.include?('premium') || name.include?('pro')
      'premium'
    else
      'free'
    end
  end
  
  def create_subscription_for_user(user, plan)
    # Cancel existing active subscription if any
    user.active_subscription&.cancel!(reason: "Admin created new subscription")
    
    # Create new subscription
    user.subscriptions.create!(
      plan: plan,
      status: :active,
      payment_provider: :manual,
      current_period_start: Time.current,
      current_period_end: calculate_period_end(plan)
    )
  end
end