# ABOUTME: Controller for managing user subscriptions and Stripe checkout flow
# ABOUTME: Handles pricing display, checkout sessions, subscription management, and billing portal access

class SubscriptionsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_subscription_service
  before_action :set_current_subscription, only: [:show, :update, :destroy, :reactivate]
  
  def index
    # Allow test purchases in production/development via a secure token
    @allow_test_purchase = false
    if (Rails.env.production? || Rails.env.development?) && STRIPE_MODE == 'test'
      # Hardcoded token for temporary testing.
      # IMPORTANT: Replace "CHANGE_ME_TO_A_REAL_SECRET_TOKEN" with your secret token.
      test_token = "TEST"
      if params[:allow_test_purchase] == test_token
        @allow_test_purchase = true
      end
    end

    # Show pricing page with available plans - exclude pilot tier (internal only)
    @plans = Plan.active.where.not(tier: 'pilot').order(:price_cents)
    @current_subscription = current_user.active_subscription
    
    # Check if user has an active paid subscription
    @has_active_standard_subscription = current_user.has_paid_subscription?
    
    # Fetch dynamic prices from Stripe API
    stripe_prices = helpers.fetch_dynamic_stripe_prices
    @monthly_price = stripe_prices[:monthly]
    @annual_price = stripe_prices[:annual]
    
    # Get plan records from database for reference
    @monthly_plan = @plans.find_by(interval: 'month', tier: 'standard')
    @annual_plan = @plans.find_by(interval: 'year', tier: 'standard')
    
    # Update plans with Stripe price IDs if they exist
    if @monthly_plan && @monthly_price
      @monthly_plan.stripe_price_id = @monthly_price[:price_id]
    end
    
    if @annual_plan && @annual_price
      @annual_plan.stripe_price_id = @annual_price[:price_id]
    end
  end
  
  def show
    # Show current subscription details
    @subscription = current_user.active_subscription
    @invoices = @subscription_service.get_invoices(limit: 5) if @subscription
    @upcoming_invoice = @subscription_service.get_upcoming_invoice if @subscription
  end
  
  def new
    # Show plan selection page - exclude pilot tier (internal only)
    @plans = Plan.active.where.not(stripe_price_id: nil).where.not(tier: 'pilot').order(:price_cents)
    @selected_plan = Plan.find_by(id: params[:plan_id]) || @plans.first
    
    # Check if user has active premium subscription using policy
    @has_active_premium = allowed_to?(:has_active_premium?, current_user, with: SubscriptionPolicy)
  end
  
  def create
    # Create Stripe checkout session
    price_id = params[:price_id]
    promo_code = params[:coupon_code]
    
    checkout_price_id = price_id
    checkout_coupon_code = nil
    plan_id_for_url = nil
    plan_name_for_display = nil

    # If a promotion code was submitted, validate it
    if promo_code.present?
      validation_result = StripePromotionCodeService.validate(
        promo_code,
        customer_email: current_user.email
      )
      
      if validation_result[:valid]
        if validation_result[:unlocked_price_id]
          # Handle special offer case
          checkout_price_id = validation_result[:unlocked_price_id]
          plan_id_for_url = 'special'
          plan_name_for_display = validation_result.dig(:unlocked_price_details, :nickname) || 'Special Offer'
        else
          # Handle regular coupon case
          checkout_coupon_code = promo_code
        end
      else
        redirect_to subscriptions_path, alert: validation_result[:error] || "Invalid promotion code."
        return
      end
    end
    
    # Find the plan from the database or create it from Stripe
    plan = Plan.find_by(stripe_price_id: checkout_price_id)

    if plan.nil?
      # Fetch from Stripe and create it
      begin
        price = Stripe::Price.retrieve(checkout_price_id)
        product = Stripe::Product.retrieve(price.product)

        plan = Plan.find_or_create_by!(stripe_price_id: price.id) do |p|
          p.name = price.nickname.present? ? "#{product.name} - #{price.nickname}" : product.name
          p.stripe_product_id = product.id
          p.price_cents = price.unit_amount
          p.interval = price.recurring&.interval || 'one_time'
          p.tier = product.metadata['tier'] || 'standard'
          p.metadata = product.metadata.to_h
          p.active = product.active
        end
      rescue Stripe::StripeError => e
        Rails.logger.error "Failed to fetch price from Stripe: #{e.message}"
        # Plan is still nil, so the check below will catch it and redirect.
      end
    end

    # If we don't have a valid plan at this point, redirect.
    unless plan
      alert_message = t('subscriptions.errors.checkout_preparation_failed', 
                        default: 'There was a problem preparing your checkout. Please try again. If the issue persists, please contact support.')
      redirect_to subscriptions_path, alert: alert_message
      return
    end

    # If we don't have a plan ID yet, use the plan from database if found
    if plan_id_for_url.nil?
      plan_id_for_url = plan.id
      plan_name_for_display = plan.name
    end
    
    # Create the local subscription record first with incomplete status
    # This eliminates the race condition where webhook hasn't arrived yet
    subscription = current_user.subscriptions.create!(
      plan: plan,
      status: :incomplete,
      payment_provider: :stripe
    )
    
    begin
      session = @subscription_service.create_checkout_session(
        checkout_price_id,
        subscription_success_url(plan_id: plan_id_for_url),
        subscription_cancel_url,
        coupon_code: checkout_coupon_code,
        client_reference_id: subscription.id # Pass our subscription ID to Stripe
      )
      
      redirect_to session.url, allow_other_host: true
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe checkout error: #{e.message}"
      # Clean up the subscription we created if checkout session fails
      subscription.destroy
      redirect_to subscriptions_path, alert: translate_stripe_error(e)
    end
  end
  
  def update
    # Update subscription (upgrade/downgrade)
    new_price_id = params[:price_id]
    new_plan = Plan.find_by!(stripe_price_id: new_price_id)
    
    begin
      @subscription_service.update_subscription(new_price_id)
      flash[:notice] = t('subscriptions.update_success', plan: new_plan.name)
      redirect_to subscription_path
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe update error: #{e.message}"
      flash[:alert] = translate_stripe_error(e)
      redirect_to subscription_path
    rescue StandardError => e
      Rails.logger.error "Subscription update error: #{e.message}"
      flash[:alert] = e.message
      redirect_to subscription_path
    end
  end
  
  def destroy
    # Cancel subscription
    at_period_end = params[:immediate] != 'true'
    reason = params[:reason]
    
    begin
      @subscription_service.cancel_subscription(
        at_period_end: at_period_end,
        reason: reason
      )
      
      # Update local subscription record
      if @current_subscription
        @current_subscription.cancel!(
          reason: reason,
          at_period_end: at_period_end
        )
      end
      
      if at_period_end
        flash[:notice] = t('subscriptions.cancel_success_period_end', 
                          date: @current_subscription.current_period_end.strftime('%B %d, %Y'))
      else
        flash[:notice] = t('subscriptions.cancel_success_immediate')
      end
      
      redirect_to subscription_path
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe cancellation error: #{e.message}"
      flash[:alert] = translate_stripe_error(e)
      redirect_to subscription_path
    rescue StandardError => e
      Rails.logger.error "Subscription cancellation error: #{e.message}"
      flash[:alert] = e.message
      redirect_to subscription_path
    end
  end
  
  def reactivate
    # Reactivate a canceled subscription
    begin
      @subscription_service.reactivate_subscription
      
      # Update local subscription record
      if @current_subscription&.can_reactivate?
        @current_subscription.reactivate!
      end
      
      flash[:notice] = t('subscriptions.reactivate_success')
      redirect_to subscription_path
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe reactivation error: #{e.message}"
      flash[:alert] = translate_stripe_error(e)
      redirect_to subscription_path
    rescue StandardError => e
      Rails.logger.error "Subscription reactivation error: #{e.message}"
      flash[:alert] = e.message
      redirect_to subscription_path
    end
  end
  
  def portal
    # Redirect to Stripe billing portal
    begin
      session = @subscription_service.create_portal_session(subscription_url)
      redirect_to session.url, allow_other_host: true
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe portal error: #{e.message}"
      flash[:alert] = translate_stripe_error(e)
      redirect_to subscription_path
    end
  end
  
  def success
    # Handle successful checkout return - validate subscription using session_id from Stripe
    
    # Check for blank session_id parameter first
    if params[:session_id].blank?
      Rails.logger.error "Success page accessed without session_id parameter"
      flash[:alert] = t('subscriptions.checkout_error_invalid_session', 
                        default: 'Invalid checkout session. Please try subscribing again.')
      return redirect_to subscriptions_path
    end
    
    begin
      # Retrieve the checkout session from Stripe using the session_id parameter
      session = Stripe::Checkout::Session.retrieve(params[:session_id])
      
      # Find the local subscription using client_reference_id
      subscription = Subscription.find_by(id: session.client_reference_id)
      
      if subscription.nil?
        Rails.logger.error "No local subscription found for client_reference_id: #{session.client_reference_id}"
        flash[:alert] = t('subscriptions.checkout_error_no_subscription')
        return redirect_to subscriptions_path
      end
      
      # If webhook is slow, status will still be 'incomplete'
      if subscription.incomplete?
        # Render a view that tells the user we are processing the subscription
        # This view can poll for status changes
        @subscription = subscription
        flash.now[:notice] = t('subscriptions.payment_processing', 
                               default: "Payment successful! We are activating your subscription...")
        render :processing_subscription
      elsif subscription.incomplete_expired?
        # Subscription exists but requires additional action (3D Secure, etc.)
        flash[:alert] = t('subscriptions.checkout_incomplete', 
                          default: 'Your subscription requires additional verification. Please check your email for instructions.')
        redirect_to subscriptions_path
      elsif subscription.can_access_features?
        # Webhook was fast, subscription is already active
        flash[:notice] = t('subscriptions.checkout_success_generic', 
                          default: "Subscription successfully activated!")
        redirect_to subscriptions_path
      else
        # Subscription exists but in unexpected state
        flash[:alert] = t('subscriptions.checkout_error_status', 
                          status: subscription.display_status,
                          default: "Subscription created but status is %{status}. Please contact support if you need help.")
        redirect_to subscriptions_path
      end
    rescue Stripe::InvalidRequestError => e
      # Handle specific case of invalid session ID
      Rails.logger.error "Invalid Stripe session ID: #{e.message}"
      flash[:alert] = t('subscriptions.checkout_error_invalid_session',
                        default: 'Invalid checkout session. Please try subscribing again.')
      return redirect_to subscriptions_path
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe success page error: #{e.message}"
      flash[:alert] = t('subscriptions.checkout_error_no_subscription')
      return redirect_to subscriptions_path
    end
  end
  
  def cancel
    # Handle canceled checkout
    flash[:alert] = t('subscriptions.checkout_canceled')
    redirect_to subscriptions_path
  end
  
  def check_status
    # Check subscription status for AJAX polling
    # Security: Scope query to current user's subscriptions to prevent information leakage
    subscription = current_user.subscriptions.find_by(id: params[:id])
    
    unless subscription
      render json: { error: 'Not Found' }, status: :not_found
      return
    end
    
    render json: {
      id: subscription.id,
      status: subscription.status,
      can_access_features: subscription.can_access_features?
    }
  end
  
  # Validate Stripe promotion codes
  def validate_coupon
    code = params[:code]&.strip
    
    if code.blank?
      render json: { valid: false, message: t('subscriptions.coupon.errors.invalid') }
      return
    end
    
    # Use the new promotion code service
    validation_result = StripePromotionCodeService.validate(
      code,
      customer_email: current_user.email
    )
    
    if validation_result[:valid]
      # If the service returned an unlocked price, pass its detailed response directly to the frontend.
      if validation_result[:unlocked_price_id]
        render json: validation_result
        return
      end
      
      # Otherwise, it's a regular coupon. Build the JSON response for the frontend script.
      discount_info = if validation_result[:percent_off]
        { 
          type: 'percentage',
          value: validation_result[:percent_off],
          message: "#{validation_result[:percent_off]}% #{t('subscriptions.coupon.off', default: 'off')}"
        }
      elsif validation_result[:amount_off]
        currency_symbol = helpers.currency_symbol(validation_result[:currency])
        amount_off = (validation_result[:amount_off] / 100.0)
        { 
          type: 'amount',
          value: amount_off,
          message: "#{currency_symbol}#{amount_off} #{t('subscriptions.coupon.amount_off', default: 'off')}"
        }
      else
        { type: 'unknown', value: 0, message: t('subscriptions.coupon.applied', default: 'Coupon applied') }
      end
      
      duration_msg = case validation_result[:duration]
      when 'forever' then t('subscriptions.coupon.forever', default: 'forever')
      when 'once' then t('subscriptions.coupon.first_payment', default: 'for the first payment')
      when 'repeating' then t('subscriptions.coupon.for_months', count: validation_result[:duration_in_months], default: "for #{validation_result[:duration_in_months]} months")
      else ''
      end
      
      render json: { 
        valid: true,
        code: code.upcase,
        discount: discount_info,
        message: t('subscriptions.coupon.applied_message', 
                   discount: discount_info[:message], 
                   duration: duration_msg,
                   default: "Coupon for #{discount_info[:message]} #{duration_msg} applied.")
      }
    else
      # Handle invalid coupon response from the service
      render json: { 
        valid: false, 
        message: validation_result[:error] || t('subscriptions.coupon.errors.generic', default: 'This coupon is not valid.')
      }
    end
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe coupon validation error: #{e.message}"
    render json: { valid: false, message: t('subscriptions.coupon.errors.generic', default: 'This coupon is not valid.') }
  rescue StandardError => e
    Rails.logger.error "Unexpected error validating coupon: #{e.class} - #{e.message}"
    render json: { valid: false, message: t('subscriptions.coupon.errors.generic', default: 'An unexpected error occurred.') }
  end
  
  private
  
  def set_subscription_service
    @subscription_service = StripeSubscriptionService.new(current_user)
  end
  
  def set_current_subscription
    @current_subscription = current_user.active_subscription
  end
  
  def subscription_success_url(plan_id: nil)
    # Stripe requires the literal {CHECKOUT_SESSION_ID} placeholder in the URL
    # It will replace this with the actual session ID during redirect
    base_url = success_subscriptions_url(plan_id: plan_id)
    "#{base_url}&session_id={CHECKOUT_SESSION_ID}"
  end
  
  def subscription_cancel_url
    cancel_subscriptions_url
  end
  
  # Helper to translate Stripe errors into user-friendly messages
  def translate_stripe_error(error)
    case error.code&.to_s
    when 'card_declined'
      t('subscriptions.errors.card_declined', default: 'Your card was declined. Please try a different payment method.')
    when 'expired_card'
      t('subscriptions.errors.expired_card', default: 'Your card has expired. Please update your payment information.')
    when 'insufficient_funds'
      t('subscriptions.errors.insufficient_funds', default: 'Your card has insufficient funds. Please try a different payment method.')
    when 'payment_intent_authentication_failure'
      t('subscriptions.errors.authentication_failed', default: 'Payment authentication failed. Please try again.')
    when 'incorrect_cvc'
      t('subscriptions.errors.incorrect_cvc', default: 'Your card\'s security code is incorrect.')
    when 'processing_error'
      t('subscriptions.errors.processing_error', default: 'An error occurred while processing your payment. Please try again.')
    when 'rate_limit'
      t('subscriptions.errors.rate_limit', default: 'Too many requests. Please wait a moment and try again.')
    else
      # Include more details in the default message if available
      if error.message.present?
        t('subscriptions.errors.stripe_message', 
          message: error.message,
          default: "Payment error: %{message}")
      else
        t('subscriptions.errors.generic', 
          default: 'An error occurred processing your payment. Please try again or contact support.')
      end
    end
  end
end