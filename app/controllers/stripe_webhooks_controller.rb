# ABOUTME: Handles incoming Stripe webhook events for subscription management
# ABOUTME: Verifies webhook signatures and processes events asynchronously via background jobs
class StripeWebhooksController < ApplicationController
  skip_before_action :verify_authenticity_token
  skip_before_action :authenticate_user!
  
  def create
    payload = request.body.read
    sig_header = request.env['HTTP_STRIPE_SIGNATURE']
    
    begin
      event = construct_webhook_event(payload, sig_header)
      
      # Process webhook asynchronously for better reliability
      StripeWebhookJob.perform_later(event.as_json)
      
      head :ok
    rescue JSON::ParserError => e
      Rails.logger.error "Invalid webhook payload: #{e.message}"
      head :bad_request
    rescue Stripe::SignatureVerificationError => e
      Rails.logger.error "Invalid webhook signature: #{e.message}"
      head :bad_request
    rescue StandardError => e
      Rails.logger.error "Webhook processing error: #{e.class.name} - #{e.message}"
      head :internal_server_error
    end
  end
  
  private
  
  def construct_webhook_event(payload, sig_header)
    # Ensure webhook secret is configured
    if webhook_secret.blank?
      Rails.logger.error "Stripe webhook secret not configured"
      raise Stripe::SignatureVerificationError.new("Webhook secret not configured", sig_header)
    end
    
    Stripe::Webhook.construct_event(
      payload, 
      sig_header, 
      webhook_secret
    )
  end
  
  def webhook_secret
    # Use the constant from initializer which handles test/live mode
    STRIPE_WEBHOOK_SECRET
  end
end