# ABOUTME: Background job for sending bulk email notifications to multiple users
# ABOUTME: Handles rate limiting and batch processing with summary logging in development

class BulkNotificationJob < ApplicationJob
  queue_as :mailers

  def perform(project, user_ids)
    # In development, always use summary logging for bulk sends
    if Rails.env.development?
      perform_with_summary_logging(project, user_ids)
    else
      perform_regular(project, user_ids)
    end
  end
  
  private
  
  def perform_with_summary_logging(project, user_ids)
    Rails.logger.info "🚀 Starting BULK EMAIL DELIVERY for #{user_ids.size} recipients"
    Rails.logger.info "Project: #{project.summary} (ID: #{project.id})"
    Rails.logger.info "-" * 60
    
    # Temporarily redirect stdout to suppress email output
    original_stdout = $stdout
    $stdout = StringIO.new if Rails.env.development?
    
    # Collect statistics
    stats = {
      total: 0,
      successful: 0,
      failed: 0,
      skipped: 0,
      by_domain: Hash.new(0),
      errors: [],
      started_at: Time.current
    }
    
    User.where(id: user_ids).find_each.with_index do |user, index|
      # Find or create tracker for idempotency
      tracker = EmailDeliveryTracker.find_or_create_by(
        project: project,
        user: user,
        email_type: 'new_project_notification'
      )
      
      # Skip if already sent to prevent duplicates
      if tracker.sent?
        stats[:skipped] += 1
        next
      end
      
      begin
        # Send the email using deliver_later to ensure rate limiting
        NotificationMailer.new_project_notification(project, user).deliver_later
        
        tracker.mark_as_queued!
        stats[:successful] += 1
        stats[:total] += 1
        
        # Track by domain
        domain = user.email.split('@').last
        stats[:by_domain][domain] += 1
        
        # Log progress every 10 users (to Rails.logger, not stdout)
        if (index + 1) % 10 == 0
          # Restore stdout temporarily for progress logging
          $stdout = original_stdout
          Rails.logger.info "  Progress: #{index + 1}/#{user_ids.size} emails processed..."
          $stdout = StringIO.new if Rails.env.development?
        end
      rescue => e
        # Restore stdout for error logging
        $stdout = original_stdout
        Rails.logger.error "  ❌ Failed for user #{user.id} (#{user.email}): #{e.message}"
        $stdout = StringIO.new if Rails.env.development?
        
        tracker.mark_as_failed!(e.message) if tracker
        stats[:failed] += 1
        stats[:total] += 1
        stats[:errors] << { user_id: user.id, email: user.email, error: e.message }
      end
    end
    
    # Restore stdout before logging summary
    $stdout = original_stdout if Rails.env.development?
    
    # Log the comprehensive summary
    duration = Time.current - stats[:started_at]
    
    Rails.logger.info ""
    Rails.logger.info "=" * 60
    Rails.logger.info "📧 BULK EMAIL DELIVERY COMPLETE"
    Rails.logger.info "=" * 60
    Rails.logger.info "Duration: #{duration.round(2)} seconds"
    Rails.logger.info "Total Emails Sent: #{stats[:total]}"
    Rails.logger.info "✅ Successful: #{stats[:successful]}"
    Rails.logger.info "❌ Failed: #{stats[:failed]}" if stats[:failed] > 0
    Rails.logger.info "⏭️  Skipped (already sent): #{stats[:skipped]}" if stats[:skipped] > 0
    
    if stats[:by_domain].any?
      Rails.logger.info "-" * 40
      Rails.logger.info "Recipients by Domain:"
      stats[:by_domain].sort_by { |_, count| -count }.each do |domain, count|
        Rails.logger.info "  #{domain}: #{count}"
      end
    end
    
    if stats[:errors].any?
      Rails.logger.info "-" * 40
      Rails.logger.info "Errors:"
      stats[:errors].each do |error|
        Rails.logger.info "  ❌ User #{error[:user_id]} (#{error[:email]}): #{error[:error]}"
      end
    end
    
    Rails.logger.info "=" * 60
    Rails.logger.info ""
  ensure
    # Always restore stdout
    $stdout = original_stdout if Rails.env.development?
  end
  
  def perform_regular(project, user_ids)
    # Original implementation for small batches or production
    Rails.logger.info "📧 Sending notifications to #{user_ids.size} users (regular mode)"
    
    User.where(id: user_ids).find_each do |user|
      # Find or create tracker for idempotency
      tracker = EmailDeliveryTracker.find_or_create_by(
        project: project,
        user: user,
        email_type: 'new_project_notification'
      )
      
      # Skip if already sent to prevent duplicates
      if tracker.sent?
        Rails.logger.info "Skipping email for user #{user.id} - already sent"
        next
      end
      
      # Queue the email and mark tracker - this WILL show in logs as normal
      NotificationMailer.new_project_notification(project, user).deliver_later
      tracker.mark_as_queued!
    rescue => e
      Rails.logger.error "Failed to queue email for user #{user.id}: #{e.message}"
      tracker&.mark_as_failed!(e.message) if tracker
    end
  end
end