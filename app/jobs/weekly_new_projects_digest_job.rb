# ABOUTME: Background job that sends weekly digest emails to eligible users about new projects
# ABOUTME: Runs weekly via cron, respects project visibility rules and 14-day restriction for free users
class WeeklyNewProjectsDigestJob < ApplicationJob
  queue_as :default
  
  def perform
    Rails.logger.info "[WEEKLY_DIGEST] Starting weekly new projects digest job at #{Time.current}"
    
    # Calculate the reporting period (last 7 days)
    period_start = 7.days.ago
    period_end = Time.current
    
    # Get all new projects published within the period
    new_projects = Project.where(
      first_published_at: period_start..period_end,
      approved: true,
      project_status: true
    )
    
    # Only proceed if there are new projects
    if new_projects.any?
      Rails.logger.info "[WEEKLY_DIGEST] Found #{new_projects.count} new projects published in the last 7 days"
      # Track who should receive notification (using Set to ensure uniqueness)
      users_to_notify = Set.new
      
      # Optimize queries by processing projects in batches by type
      # This avoids N+1 queries and uses parameterized queries for security
      
      # Get all network_only and semi_public projects
      network_only_projects = new_projects.select(&:network_only?)
      semi_public_projects = new_projects.select(&:semi_public?)
      
      # Process semi_public projects in one query (most efficient)
      if semi_public_projects.any?
        all_user_ids = User.active.where(approved: true).pluck(:id)
        users_to_notify.merge(all_user_ids)
        Rails.logger.info "[WEEKLY_DIGEST] #{semi_public_projects.count} semi_public projects - adding #{all_user_ids.count} active users"
      end
      
      # Process network_only projects - use batch query to avoid N+1
      if network_only_projects.any?
        owner_ids = network_only_projects.map(&:user_id).uniq
        network_users = User.connected_to_any_of(owner_ids)
                            .active
                            .where(approved: true)
                            .pluck(:id)
        
        users_to_notify.merge(network_users)
        Rails.logger.info "[WEEKLY_DIGEST] #{network_only_projects.count} network_only projects - adding #{network_users.count} network users"
      end
      
      Rails.logger.info "[WEEKLY_DIGEST] Total unique users to notify: #{users_to_notify.size}"
      
      # Get the actual user records for those who should be notified
      # Only send to users with active subscriptions (exclude free users and super_boss)
      eligible_users = User.where(id: users_to_notify.to_a)
                           .includes(:user_profile)
                           .with_paid_subscription
      
      Rails.logger.info "[WEEKLY_DIGEST] Sending digest to #{eligible_users.count} eligible users"
      
      # Send digest email to each eligible user
      eligible_users.find_each do |user|
        begin
          NotificationMailer.weekly_new_projects_digest(user).deliver_later
          Rails.logger.info "[WEEKLY_DIGEST] Queued digest email for user #{user.id} (#{user.email})"
        rescue => e
          Rails.logger.error "[WEEKLY_DIGEST] Failed to queue email for user #{user.id}: #{e.message}"
          # Continue processing other users even if one fails
        end
      end
      
      Rails.logger.info "[WEEKLY_DIGEST] Completed sending digest emails to #{eligible_users.count} subscribed users"
    else
      Rails.logger.info "[WEEKLY_DIGEST] No new projects found in the last 7 days, skipping digest emails"
    end
    
    Rails.logger.info "[WEEKLY_DIGEST] Weekly digest job completed at #{Time.current}"
  end
end