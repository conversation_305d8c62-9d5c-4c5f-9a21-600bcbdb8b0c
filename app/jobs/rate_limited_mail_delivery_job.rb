class RateLimitedMailDeliveryJob < ApplicationJob
  class ResendRateLimitError < StandardError; end
  
  include GoodJob::ActiveJobExtensions::Concurrency
  
  # Set queue priority for email jobs
  queue_as :mailers
  
  # Officially documented way to rate limit:
  # Conservative rate: 1 email per 1.5 seconds to avoid timing precision issues
  # This ensures we stay well below Resend's 2 emails/second limit
  good_job_control_concurrency_with(
    perform_throttle: [1, 1.5.seconds],
    key: 'resend_api_rate_limit',
    total_limit: 1  # Ensure single processing to prevent race conditions
  )
  
  # Retry on Resend's rate limit error with exponential backoff.
  retry_on ResendRateLimitError, wait: :exponentially_longer, attempts: 5
  
  def perform(mailer_class_name, mailer_method, delivery_method, delivery_args)
    mailer_class = mailer_class_name.constantize
    args = delivery_args.fetch(:args, [])
    kwargs = delivery_args.fetch(:kwargs, {})
    params = delivery_args.fetch(:params, {})

    mailer = build_mailer(mailer_class, mailer_method, args: args, kwargs: kwargs, params: params)
    mailer.send(delivery_method)
    
    # Update tracker after successful delivery
    update_tracker_status(mailer_method, args, params, 'sent')
  rescue Resend::Error => e
    # Only retry on a specific rate limit error, not other API errors.
    if e.message.include?('rate_limit_exceeded')
      raise ResendRateLimitError, "Resend API rate limit exceeded, retrying."
    else
      # Mark tracker as failed for non-retriable errors
      update_tracker_status(mailer_method, args, params, 'failed', e.message)
      Rails.logger.error "Email delivery failed (non-retriable Resend error): #{e.message}"
      # For other errors (e.g., validation), don't retry.
      raise e
    end
  rescue StandardError => e
    # Mark tracker as failed for other errors
    update_tracker_status(mailer_method, args, params, 'failed', e.message)
    Rails.logger.error "Email delivery failed (general error): #{e.class.name}: #{e.message}"
    Rails.logger.error "Backtrace: #{e.backtrace.first(3).join("\n")}" if e.backtrace
    raise e
  end

  private

  def build_mailer(mailer_class, mailer_method, args:, kwargs:, params:)
    if params.any?
      mailer = mailer_class.with(params)
    else
      mailer = mailer_class
    end
    
    if kwargs.any?
      mailer.send(mailer_method, *args, **kwargs)
    else
      mailer.send(mailer_method, *args)
    end
  end
  
  def update_tracker_status(mailer_method, args, params, status, error_message = nil)
    # Map mailer methods to email types
    email_type = case mailer_method.to_s
    when 'new_project_notification'
      'new_project_notification'
    when 'admin_project_notification'
      'admin_project_notification'
    when 'user_approval_request_notification'
      'user_approval_request'
    when 'access_request_notification'
      'access_request'
    when 'approved_access_notification'
      'approved_access'
    else
      # If we don't recognize the method, don't try to update a tracker
      return
    end
    
    # Extract project and user from args (they're typically the first two arguments)
    # For NotificationMailer methods, the pattern is usually (project, user) or just (user)
    project = args[0] if args[0].is_a?(Project)
    user = args[1] if args[1].is_a?(User)
    user ||= args[0] if args[0].is_a?(User)
    
    # Some mailers might not have a project (e.g., user approval requests)
    if project && user
      tracker = EmailDeliveryTracker.find_by(
        project: project,
        user: user,
        email_type: email_type
      )
    elsif user && email_type == 'user_approval_request'
      # Special case for user approval requests that don't have a project
      tracker = EmailDeliveryTracker.where(
        user: user,
        email_type: email_type,
        status: 'queued'
      ).order(created_at: :desc).first
    else
      # Can't find tracker without proper context
      return
    end
    
    return unless tracker
    
    # Update the tracker status
    case status
    when 'sent'
      tracker.mark_as_sent!
      Rails.logger.info "Email tracker #{tracker.id} marked as sent"
    when 'failed'
      tracker.mark_as_failed!(error_message)
      Rails.logger.error "Email tracker #{tracker.id} marked as failed: #{error_message}"
    end
  rescue => e
    # Don't let tracker update failures break email delivery
    Rails.logger.error "Failed to update email tracker: #{e.message}"
  end
end