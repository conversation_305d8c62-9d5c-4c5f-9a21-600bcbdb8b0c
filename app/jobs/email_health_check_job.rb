# ABOUTME: Background job for monitoring email delivery health and alerting on issues
# ABOUTME: Runs hourly to check for failed or stuck email deliveries

class EmailHealthCheckJob < ApplicationJob
  queue_as :default
  
  def perform
    # Check for failed deliveries
    failed_count = EmailDeliveryTracker.failed.count
    
    # Check for stuck pending emails (older than 1 hour)
    stuck_count = EmailDeliveryTracker.stuck.count
    
    # Alert if thresholds exceeded
    if failed_count > 10 || stuck_count > 20
      Rails.logger.error "EMAIL HEALTH CHECK ALERT: #{failed_count} failed deliveries, #{stuck_count} stuck emails"
      
      # Send alert to admins
      alert_admins(failed_count, stuck_count)
    else
      Rails.logger.info "Email health check passed: #{failed_count} failed, #{stuck_count} stuck"
    end
    
    # Log statistics
    log_email_statistics
  end
  
  private
  
  def alert_admins(failed_count, stuck_count)
    # Get super_boss users for alerting
    admin_emails = User.where(role: :super_boss).pluck(:email)
    
    if admin_emails.any?
      # In production, this would send an alert email
      # For now, we log the alert
      Rails.logger.error "Would send alert to admins: #{admin_emails.join(', ')}"
      Rails.logger.error "Alert: #{failed_count} failed email deliveries, #{stuck_count} stuck in queue"
    end
  end
  
  def log_email_statistics
    stats = {
      total: EmailDeliveryTracker.count,
      pending: EmailDeliveryTracker.pending.count,
      sent: EmailDeliveryTracker.sent.count,
      failed: EmailDeliveryTracker.failed.count,
      stuck: EmailDeliveryTracker.stuck.count,
      last_24h_sent: EmailDeliveryTracker.sent.where('sent_at > ?', 24.hours.ago).count
    }
    
    Rails.logger.info "Email delivery statistics: #{stats.inspect}"
  end
end