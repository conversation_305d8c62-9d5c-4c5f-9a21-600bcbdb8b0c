# ABOUTME: Helper methods for subscription views and Stripe price management
# ABOUTME: Provides cached Stripe price fetching to avoid excessive API calls

module SubscriptionHelper
  # Dynamically fetch paid prices from Stripe based on product metadata
  # STANDARD - currently only getting this one product
  
  def fetch_dynamic_stripe_prices
    Rails.cache.fetch("dynamic_stripe_prices_#{stripe_mode}", expires_in: 1.hour) do
      begin
        # Fetch all products and prices in a single batch to avoid N+1 queries
        products = Stripe::Product.list(limit: 100, active: true)
        
        # Collect all product IDs that need price fetching
        paid_product_ids = products.data.select do |product|
          product.name.downcase.include?('standard') || 
          product.metadata['tier'] == 'standard' ||
          product.metadata['subscription_tier'] == 'standard'
        end.map(&:id)
        
        # Fetch all prices for premium products in one API call
        all_prices = if paid_product_ids.any?
          Stripe::Price.list(
            limit: 100,
            active: true,
            lookup_keys: nil,
            expand: ['data.product']
          ).data.select { |price| paid_product_ids.include?(price.product) }
        else
          []
        end
        
        paid_monthly = nil
        paid_annual = nil
        
        # Process products with their prices already loaded
        products.data.each do |product|
          next unless paid_product_ids.include?(product.id)
          
          # First check default price if available
          if product.default_price
            price = if product.default_price.is_a?(String)
              # Find in our pre-fetched prices to avoid extra API call
              all_prices.find { |p| p.id == product.default_price } ||
                Stripe::Price.retrieve(product.default_price)
            else
              product.default_price
            end
            
            if price&.recurring
              case price.recurring.interval
              when 'month'
                paid_monthly ||= {
                  price_id: price.id,
                  amount: price.unit_amount / 100.0,
                  currency: price.currency.upcase,
                  product_id: product.id,
                  product_name: product.name
                }
              when 'year'
                paid_annual ||= {
                  price_id: price.id,
                  amount: price.unit_amount / 100.0,
                  currency: price.currency.upcase,
                  product_id: product.id,
                  product_name: product.name
                }
              end
            end
          end
          
          # Check other prices from our pre-fetched list
          if paid_monthly.nil? || paid_annual.nil?
            product_prices = all_prices.select { |p| p.product == product.id }
            
            product_prices.each do |price|
              next unless price.recurring
              
              case price.recurring.interval
              when 'month'
                paid_monthly ||= {
                  price_id: price.id,
                  amount: price.unit_amount / 100.0,
                  currency: price.currency.upcase,
                  product_id: product.id,
                  product_name: product.name
                }
              when 'year'
                paid_annual ||= {
                  price_id: price.id,
                  amount: price.unit_amount / 100.0,
                  currency: price.currency.upcase,
                  product_id: product.id,
                  product_name: product.name
                }
              end
              
              # Early exit if we found both
              break if paid_monthly && paid_annual
            end
          end
          
          # Early exit if we found both prices
          break if paid_monthly && paid_annual
        end
        
        {
          monthly: paid_monthly,
          annual: paid_annual
        }
      rescue Stripe::StripeError => e
        Rails.logger.error "Failed to fetch dynamic Stripe prices: #{e.message}"
        { monthly: nil, annual: nil }
      end
    end
  end
  
  # Clear Stripe price cache (useful for admin operations)
  def clear_stripe_price_cache
    Rails.cache.delete_matched("dynamic_stripe_prices*")
    Rails.cache.delete_matched("stripe_price*")
  end
  
  private
  
  def stripe_mode
    STRIPE_MODE rescue 'test'
  end
end