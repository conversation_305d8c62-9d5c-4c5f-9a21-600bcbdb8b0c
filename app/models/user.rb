class User < ApplicationRecord

  #attr_accessor :skip_inviter_profile_check
  
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :invitable, :database_authenticatable, :registerable, :recoverable, :rememberable, :validatable, :confirmable, :lockable, :timeoutable, :trackable

  enum role: { regular: 0, super_boss: 2 }  
  enum subscription_tier: { free: 0, standard: 1, pilot: 2 }, _prefix: :tier

  # Subscription associations - CRITICAL: has_many for full history!
  has_many :subscriptions, dependent: :destroy
  has_one :active_subscription, 
          -> { where(status: [:active, :trialing])
              .joins(:plan)
              .order(Arel.sql("CASE plans.tier 
                               WHEN #{Plan.tiers['pilot']} THEN 0 
                               WHEN #{Plan.tiers['standard']} THEN 1 
                               WHEN #{Plan.tiers['free']} THEN 2 
                               END"), 
                     created_at: :desc) }, 
          class_name: 'Subscription'
  
  # Helper associations for subscriptions
  has_many :canceled_subscriptions, 
           -> { where(status: :canceled) }, 
           class_name: 'Subscription'
  has_many :plans, through: :subscriptions

  # Changed to be reciprocal, bidirectional connection in related methods
  has_many :network_connections, foreign_key: :inviter_id, dependent: :destroy
  has_many :connected_users, through: :network_connections, source: :invitee
  
  has_many :inverse_network_connections, class_name: 'NetworkConnection', foreign_key: :invitee_id, dependent: :destroy
  
  has_many :connection_requests, foreign_key: :inviter_id, dependent: :destroy
  has_many :inverse_connection_requests, class_name: 'ConnectionRequest', foreign_key: :invitee_id, dependent: :destroy
  
  has_many :projects, dependent: :destroy
  has_many :wants, dependent: :destroy
  has_many :project_auths, dependent: :destroy
  has_many :accessible_projects, through: :project_auths, source: :project
  has_many :uploads, dependent: :destroy

  has_one :user_profile, dependent: :destroy

  #validate :inviter_has_profile_name, on: :invitation_created
  # validate :validate_invitation_if_required  # Commented out to allow registration without invitation

  after_create :create_user_profile
  after_invitation_accepted :create_network_connection

  scope :active, -> { where.not(invitation_accepted_at: nil).where.not(confirmed_at: nil).where(locked_at: nil) }
  scope :approved, -> { where(approved: true) }
  scope :with_paid_subscription, -> {
    # Only regular users with active subscriptions (excludes super_boss and free users)
    left_joins(:active_subscription)
    .where.not(role: roles[:super_boss])
    .where.not(subscriptions: { id: nil })
    .distinct
  }


  
  def network_connections
    NetworkConnection.where('inviter_id = ? OR invitee_id = ?', id, id)
  end

  def super_admin?
    role == "super_boss"
  end

  def admin?
    role == "super_boss"
  end
  
  def full_name
    return user_profile.full_name if user_profile&.full_name.present?
    email.split('@').first
  end

  def connected_users_bidirectional
    # Use existing NetworkConnection.for_user scope and Rails associations
    # Gets users from both sides of bidirectional connections
    User.where(
      id: NetworkConnection.where(inviter_id: id).select(:invitee_id)
    ).or(
      User.where(
        id: NetworkConnection.where(invitee_id: id).select(:inviter_id)
      )
    )
  end

  # Batch version of connected_users_bidirectional for multiple users
  # Follows the exact same pattern but for multiple user IDs at once
  def self.connected_to_any_of(user_ids)
    return none if user_ids.blank?
    
    where(
      id: NetworkConnection.where(inviter_id: user_ids).select(:invitee_id)
    ).or(
      where(
        id: NetworkConnection.where(invitee_id: user_ids).select(:inviter_id)
      )
    )
  end

  # Delegation for backwards compatibility
  delegate :plan, to: :active_subscription, allow_nil: true
  
  # New subscription model methods
  def current_plan
    active_subscription&.plan
  end
  
  def subscription_history
    subscriptions.includes(:plan).order(created_at: :desc)
  end
  
  def ever_subscribed?
    subscriptions.exists?
  end
  
  def active_tier
    active_subscription&.plan&.tier || 'free'
  end
  
  # New subscription check methods - single source of truth
  def has_paid_subscription?
    return true if admin? # Admins always have access
    active_subscription&.can_access_features? || false
  end
  
  def free_tier?
    !has_paid_subscription?
  end
  
  # Feature-specific permission checks
  def can_create_summary_only_projects?
    has_paid_subscription?
  end
  
  def can_request_project_access?
    has_paid_subscription?
  end
  
  def can_view_recent_projects?
    has_paid_subscription?
  end
  
  def can_request_access_to?(project)
    # Free users cannot request access to summary-only projects
    return true unless free_tier?
    !project.summary_only?
  end

  # Legacy subscription tier methods (backwards compatibility)
  # @deprecated Use #has_paid_subscription? instead. Kept for backwards compatibility.
  def active_subscription?
    # First check new subscription model
    if active_subscription.present?
      return active_subscription.can_access_features?
    end
    
    # Fall back to legacy logic
    return true if tier_pilot? # Pilot never expires
    return false if tier_free?
    tier_standard? && subscription_expires_at.present? && subscription_expires_at > Time.current
  end

  def subscription_expired?
    # First check new subscription model
    if subscriptions.any?
      return active_subscription.nil? || active_subscription.expired?
    end
    
    # Fall back to legacy logic
    # An expired subscription is one where a paid user's subscription has expired
    # Free users don't have "expired" subscriptions, they just don't have active ones
    tier_standard? && !active_subscription?
  end

  def generate_referral_code!
    loop do
      self.referral_code = SecureRandom.alphanumeric(8).upcase
      break unless User.exists?(referral_code: referral_code)
    end
    save!
  end

  # Approval system methods
  def approve!
    transaction do
      update!(approved: true)
      
      # If user is not yet invitation accepted, simulate invitation acceptance
      if invitation_accepted_at.nil?
        update!(invitation_accepted_at: Time.current)
        # Trigger network connection creation if needed
        create_network_connection if invited_by_id.present?
      end
      
      # Send approval notification email if needed
      # UserMailer.approval_notification(self).deliver_later
    end
  end

  def disapprove!
    update!(approved: false)
    # Send disapproval notification email if needed
    # UserMailer.disapproval_notification(self).deliver_later
  end

  # Override Devise method to allow all users to login (approved and unapproved)
  def active_for_authentication?
    super
  end

  # Custom message for unapproved users
  def inactive_message
    super
  end

  
  private

  # def validate_invitation_if_required
  #   if ENV.fetch('INVITE_ONLY', 'false') == 'true' && 
  #      new_record? && 
  #      !invitation_token.present?
  #     errors.add(:base, 'Registration requires an invitation')
  #   end
  # end

  # invited_by is a method provided by devise_invitable that returns the User who sent the invitation
  # invited_by is the User who originally sent the invitation
  # self is the User accepting the invitation
  # also checking if the User existis in the database already
  def create_network_connection
    inviter = User.find(invited_by_id)
    existing_connection = NetworkConnection.where(
      '(inviter_id = :user1 AND invitee_id = :user2) OR (inviter_id = :user2 AND invitee_id = :user1)',
      user1: inviter.id,
      user2: self.id
    ).first

    if existing_connection.nil?
      NetworkConnection.create(
        inviter_id: inviter.id,
        invitee_id: self.id
      )
    end

  end

  def create_user_profile
    self.build_user_profile(email: self.email, first_name: self.email.split('@').first, last_name: '', location: '').save
  end

  def inviter_has_profile_name
    if invited_by.present? && (!invited_by.user_profile&.first_name.present? || !invited_by.user_profile&.last_name.present?)
      errors.add(:base, 'You must have a name in your profile before inviting anyone.')
    end
  end

  
end
