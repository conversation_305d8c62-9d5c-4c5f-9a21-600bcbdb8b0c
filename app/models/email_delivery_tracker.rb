# ABOUTME: Model for tracking email delivery status to ensure idempotent notifications
# ABOUTME: Prevents duplicate emails and provides audit trail for notification delivery

class EmailDeliveryTracker < ApplicationRecord
  belongs_to :project
  belongs_to :user
  
  validates :email_type, presence: true
  validates :status, inclusion: { in: %w[pending queued sent failed] }
  
  # Scopes for monitoring
  scope :pending, -> { where(status: 'pending') }
  scope :failed, -> { where(status: 'failed') }
  scope :sent, -> { where(status: 'sent') }
  scope :stuck, -> { pending.where('created_at < ?', 1.hour.ago) }
  
  # Check if email was already sent
  def sent?
    sent_at.present? || status == 'sent'
  end
  
  # Mark as queued for delivery
  def mark_as_queued!
    update!(status: 'queued')  # Don't set sent_at here - only when actually sent
  end
  
  # Mark as successfully sent
  def mark_as_sent!
    update!(status: 'sent', sent_at: Time.current)
  end
  
  # Mark as failed with retry increment
  def mark_as_failed!(error_message = nil)
    update!(
      status: 'failed',
      retry_count: retry_count + 1
    )
    Rails.logger.error "Email delivery failed for #{email_type} to user #{user_id} for project #{project_id}: #{error_message}"
  end
end