# ABOUTME: Plan model representing subscription tiers and pricing for different payment providers
# ABOUTME: Supports Stripe, Paddle, LemonSqueezy with full metadata storage and price tracking
class Plan < ApplicationRecord
  # Associations
  has_many :subscriptions
  has_many :users, through: :subscriptions

  # Enums
  enum tier: {
    free: 0,
    standard: 1,
    pilot: 2
  }

  enum interval: {
    one_time: 0,
    month: 1,
    year: 2
  }

  # Validations
  validates :name, presence: true, uniqueness: true
  validates :tier, presence: true
  validates :interval, presence: true
  validates :price_cents, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :stripe_price_id, uniqueness: { allow_nil: true }

  # Scopes
  scope :active, -> { where(active: true) }
  scope :inactive, -> { where(active: false) }
  scope :by_tier, ->(tier) { where(tier: tier) }
  scope :free_plans, -> { where(tier: :free) }
  scope :paid_plans, -> { where.not(tier: :free) }

  # Methods
  def free?
    tier == 'free'
  end

  def paid?
    !free?
  end

  def display_name
    "#{name} - #{formatted_price}"
  end

  def formatted_price
    if price_cents == 0
      "Free"
    else
      price = price_cents / 100.0
      case interval
      when 'month'
        "$#{price}/month"
      when 'year'
        "$#{price}/year"
      when 'one_time'
        "$#{price} (one-time)"
      else
        "$#{price}"
      end
    end
  end

  def stripe_configured?
    stripe_price_id.present? && stripe_product_id.present?
  end

  def paddle_configured?
    paddle_price_id.present?
  end

  def lemonsqueezy_configured?
    lemonsqueezy_variant_id.present?
  end

  # Find plan by any provider ID
  def self.find_by_provider_id(provider_id)
    where("stripe_price_id = ? OR paddle_price_id = ? OR lemonsqueezy_variant_id = ?",
          provider_id, provider_id, provider_id).first
  end
end