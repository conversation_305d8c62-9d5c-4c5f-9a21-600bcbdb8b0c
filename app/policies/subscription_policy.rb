# ABOUTME: Authorization policy for subscription-based feature access control
# ABOUTME: Defines rules for standard and pilot tier features using ActionPolicy
class SubscriptionPolicy < ApplicationPolicy
  # Check if user has access to standard features
  def standard_feature?
    return true if user.admin? # Admins always have access
    
    subscription = user.active_subscription
    return false unless subscription&.can_access_features?
    
    plan = subscription.plan
    return false unless plan
    
    ['standard', 'pilot'].include?(plan.tier)
  end
  
  # Check if user has access to pilot features
  def pilot_feature?
    return true if user.admin? # Admins always have access
    
    subscription = user.active_subscription
    return false unless subscription&.can_access_features?
    
    plan = subscription.plan
    return false unless plan
    
    # Only pilot tier has access to pilot features
    plan.tier == 'pilot'
  end
  
  # Check if user can manage their subscription
  def manage?
    # Users can always manage their own subscriptions
    record == user
  end
  
  # Check if user can create a new subscription
  def create?
    # User must not have an active subscription
    !user.active_subscription&.can_access_features?
  end
  
  # Check if user can update their subscription
  def update?
    # User must have an active subscription to update it
    user.active_subscription&.can_access_features?
  end
  
  # Check if user can cancel their subscription
  def cancel?
    # User must have an active subscription to cancel it
    subscription = user.active_subscription
    subscription&.can_access_features? && !subscription.cancel_at_period_end?
  end
  
  # Check if user can reactivate their subscription
  def reactivate?
    # User must have a canceled subscription that hasn't ended yet
    user.active_subscription&.can_reactivate?
  end
  
  # Check if user has any active premium subscription
  def has_active_premium?
    user.subscriptions.active.any?
  end
  
  # Check specific feature access
  def access_feature?(feature_name)
    return true if user.admin?
    
    subscription = user.active_subscription
    return false unless subscription&.can_access_features?
    
    plan = subscription.plan
    return false unless plan
    
    # Check if the plan includes this feature
    plan.features&.dig(feature_name.to_s) == true
  end
  
  # Usage limits placeholder
  def within_limit?(resource_type, current_count)
    return true if user.admin?
    
    subscription = user.active_subscription
    return check_free_tier_limit(resource_type, current_count) unless subscription&.can_access_features?
    
    plan = subscription.plan
    return check_free_tier_limit(resource_type, current_count) unless plan
    
    # Get the limit for this resource type from plan
    limit = plan.features&.dig('limits', resource_type.to_s)
    return true if limit.nil? # No limit defined means unlimited
    return true if limit == -1 # -1 means unlimited
    
    current_count < limit
  end
  
  private
  
  def check_free_tier_limit(resource_type, current_count)
    # Free tier limits placeholder
    free_limits = {
      'projects' => 3,
      'wants' => 3,
      'network_connections' => 5,
      'file_uploads_mb' => 100,
      'api_calls_per_month' => 100
    }
    
    limit = free_limits[resource_type.to_s]
    return false if limit.nil? # Not allowed in free tier
    
    current_count < limit
  end
end