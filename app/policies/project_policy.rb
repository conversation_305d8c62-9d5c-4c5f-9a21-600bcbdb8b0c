# ABOUTME: Authorization policy for projects
# ABOUTME: Handles access control for project viewing, editing, and management
class ProjectPolicy < ApplicationPolicy
  
  # ApplicationPolicy uses a user object for authorization purposes
  # Policies are to be described clearly 
  # Use granular methods to define permissions
  # False negative is better than false positive in private show and files access
  
  def index?
    # Everyone can see index, but what they see is controlled in scopes
    true
  end

  def show?
    # Can see full details if:
    # 1. Owner of project (even if not approved)
    # 2. Has explicit ProjectAuth AND project is approved
    # ProjectAuth has 4 levels: no_access, pending, summary, full. 
    # Only full allows any access to private resources
    # 3. Free users can only see projects older than 14 days
    return true if owner?
    return false unless record.approved?
    return false if user.free_tier? && project_is_recent?
    
    record.project_auths.exists?(user_id: user.id, access_level: 'full_details')
  end

  def view_full_details?
    # Owner always has access (even if not approved)
    return true if owner?
    
    # Project must be approved for any non-owner access
    return false unless record.approved?
    
    # Free users have restricted access to recent projects
    return false if user.free_tier? && project_is_recent?
    
    # Explicit ProjectAuth grants (existing behavior)
    return true if record.project_auths.exists?(user_id: user.id, access_level: 'full_details')
    
    # Honor full_access setting for automatic access
    if record.full_access?
      if record.semi_public?
        return true  # All authenticated users get access
      elsif record.network_only?
        return user_connected_to_project_owner?
      end
    end
    
    false
  end
  
  # def access_files?
  #   # 1. Owner of project
  #   # 2. Has explicit ProjectAuth.
  #   record.user_id == user.id || 
  #     record.project_auths.exists?(user_id: user.id, access_level: 'full_details')
  # end

  def grant_full_details?
    manage_access?
  end

  def deny_access?
    manage_access?
  end

  def delete_access?
    manage_access?
  end

  def manage_access?
    # Ensures only the project owner can approve or deny access
    owner?
  end
  
  def create?
    # All authenticated users can create projects
    true
  end
  
  # Check if user can create or update summary-only projects
  def create_summary_only?
    user.can_create_summary_only_projects?
  end

  def upload_files?
    # Only project owner can upload files
    owner?
  end

  def edit?
    # Only owner of project can edit
    owner?
  end

  def update?
    # Only owner of project can update
    # Check summary_only flag changes
    return false unless owner?
    
    # If trying to set summary_only flag, check permission
    if record.will_save_change_to_summary_only? && record.summary_only?
      return user.can_create_summary_only_projects?
    end
    
    true
  end

  def destroy?
    # Only owner of project can destroy
    owner?
  end

  def destroy_file?
    # Only owner of project can destroy files
    owner?
  end

  def bulk_delete_files?
    # Only owner of project can bulk delete files
    owner?
  end

  private
  
  def owner?
    record.user_id == user.id
  end
  
  def project_is_recent?
    # Project published within last 14 days
    record.first_published_at.present? && record.first_published_at > 14.days.ago
  end

  def user_connected_to_project_owner?
    NetworkConnection.where(
      '(inviter_id = ? AND invitee_id = ?) OR (inviter_id = ? AND invitee_id = ?)',
      user.id, record.user_id, record.user_id, user.id
    ).exists?
  end
end