class ApplicationPolicy < ActionPolicy::Base
  authorize :user

  def manage?
    user.super_admin?
  end

  def admin_access?
    user.admin?
  end

  # Subscription tier access control methods
  def standard_feature_access?
    user.present? && user.active_subscription?
  end
  
  def pilot_feature_access?
    # Check new subscription model first, fall back to legacy field
    if user.present?
      # Check if active subscription has pilot tier
      if user.active_subscription&.plan&.tier == 'pilot'
        return true
      end
      # Fall back to legacy tier field for backwards compatibility
      user.tier_pilot?
    else
      false
    end
  end
  
end