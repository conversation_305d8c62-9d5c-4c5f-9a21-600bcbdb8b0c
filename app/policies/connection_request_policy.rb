class ConnectionRequestPolicy < ApplicationPolicy
  
  # If the ConnectionRequest belongs to the user
  def show?
    record.invitee_id == user.id || record.inviter_id == user.id
  end
  
  # If the User is in the NetworkConnection of the Project Owner or if the Project is available to the All Network
  def new?
    can_request_project_access?
  end

  # User can request access if:
  # 1. They're in the project owner's network, or
  # 2. The project is semi-public
  def create_auth_request?
    can_request_project_access?
  end

  # 1. User is the invitee (project owner) of the connection request
  # 2. Double check that the user is the owner of the project being requested
  # + Cannot approve own request
  # + Secure comparison of IDs to prevent timing attacks
  # + User must be both invitee and project owner
  def accept_auth_request?
    return false unless record.project.present?
    return false unless record.status == "pending"
    return false if record.inviter_id == user.id
    
    invitee_matches = ActiveSupport::SecurityUtils.secure_compare(
      record.invitee_id.to_s,
      user.id.to_s
    )
    owner_matches = ActiveSupport::SecurityUtils.secure_compare(
      record.project.user_id.to_s,
      user.id.to_s
    )
    Rails.logger.info(
      "Auth request check - request: #{record.id}, " \
      "user: #{user.id}, result: #{invitee_matches && owner_matches}"
    )

    invitee_matches && owner_matches
  end

  def reject_auth_request?
    accept_auth_request?  # Same checks apply
  end

  # If the ConnectionRequest belongs to the user which is the invitee
  def accept_network_request?
    record.invitee_id == user.id
  end

  def reject_network_request?
    accept_network_request?
  end

  # If the ConnectionRequest belongs to the user which is the inviter
  def destroy?
    record.inviter_id == user.id
  end


  private

  def project_owner_check?
    # Safely handle nil project
    return false unless record.project.present?
    
    # Check both conditions
    record.invitee_id == user.id && 
      record.project.user_id == user.id
  end

  def can_request_project_access?
    project = record.project
    return false unless project
    
    # Free users cannot request access to summary-only projects
    if project.summary_only? && !user.can_request_project_access?
      return false
    end

    project.semi_public? || 
      NetworkConnection.where(inviter: [project.user_id, user.id])
                      .where(invitee: [project.user_id, user.id])
                      .exists?
  end

  # def user_in_network?
  #   puts " \n--------- user_in_network?--------- \n\n "
  #   puts " \n---------#{record.project.user.network_connection.connected_to?(user)} --------- \n\n "
  #   record.project.user.network_connection.connected_to?(user)
  # end

  # def project_semi_public?
  #   puts " \n--------- project_semi_public?--------- \n\n "
  #   puts " \n---------#{record.project.semi_public && !record.project.network_only} --------- \n\n "
  #   record.project.semi_public && !record.project.network_only
  # end

end