# ABOUTME: Centralized service for handling Stripe Promotion Codes
# ABOUTME: Uses Stripe's Promotion Code API for efficient customer-facing code lookups

class StripePromotionCodeService
  class << self
    # Find promotion code by customer-facing code
    # Returns the promotion code object with associated coupon
    def find_by_code(code)
      return nil if code.blank?
      
      begin
        # Use Stripe's Promotion Code API to find by exact code
        promotion_codes = Stripe::PromotionCode.list(
          code: code.strip,
          limit: 1
        )
        
        if promotion_codes.data.any?
          promotion_code = promotion_codes.data.first
          
          # Verify the promotion code is active
          if promotion_code.active
            Rails.logger.info "Found active promotion code: #{code}"
            return promotion_code
          else
            Rails.logger.warn "Promotion code '#{code}' exists but is inactive"
          end
        else
          Rails.logger.warn "No promotion code found for: #{code}"
        end
      rescue Stripe::StripeError => e
        Rails.logger.error "Error searching for promotion code '#{code}': #{e.message}"
      end
      
      nil
    end
    
    # Get the coupon ID from a promotion code or coupon name
    # First tries promotion code lookup, then falls back to direct coupon ID
    def get_coupon_id(code_or_name)
      return nil if code_or_name.blank?
      
      # First try to find as a promotion code
      promotion_code = find_by_code(code_or_name)
      if promotion_code
        return promotion_code.coupon.id
      end
      
      # If not found as promotion code, try as direct coupon ID
      # (for backwards compatibility or admin usage)
      begin
        coupon = Stripe::Coupon.retrieve(code_or_name)
        if coupon && coupon.valid
          Rails.logger.info "Found valid coupon by ID: #{code_or_name}"
          return coupon.id
        end
      rescue Stripe::InvalidRequestError => e
        # Not a valid coupon ID, that's ok
        Rails.logger.debug "Not a valid coupon ID: #{code_or_name}"
      rescue Stripe::StripeError => e
        Rails.logger.error "Error retrieving coupon: #{e.message}"
      end
      
      nil
    end
    
    # Validate if a promotion code is valid and applicable
    def validate(code, customer_email: nil)
      promotion_code = find_by_code(code)
      return { valid: false, error: "Invalid promotion code" } unless promotion_code

      # Check for our custom metadata field first
      unlocked_price_id = promotion_code.metadata['unlocked_price_id']

      # If it's an unlock code, we can return a special valid response immediately
      if unlocked_price_id.present?
        # Additionally, fetch the price details to display on the frontend
        begin
          unlocked_price = Stripe::Price.retrieve(unlocked_price_id)
          return {
            valid: true,
            unlocked_price_id: unlocked_price_id,
            unlocked_price_details: {
              amount: (unlocked_price.unit_amount / 100.0).floor, # Convert cents to base unit
              currency: unlocked_price.currency,
              interval: unlocked_price.recurring.interval,
              nickname: unlocked_price.nickname # <-- ADD THIS
            },
            message: unlocked_price.nickname || "Special offer code applied!"
          }
        rescue Stripe::StripeError => e
          Rails.logger.error "Could not retrieve unlocked price '#{unlocked_price_id}': #{e.message}"
          return { valid: false, error: "Invalid special offer configuration" }
        end
      end
      
      coupon = promotion_code.coupon
      
      # Check if coupon is still valid
      unless coupon.valid
        return { valid: false, error: "This promotion has expired" }
      end
      
      # Check redemption limits
      if promotion_code.max_redemptions && 
         promotion_code.times_redeemed >= promotion_code.max_redemptions
        return { valid: false, error: "This promotion code has reached its usage limit" }
      end
      
      # Check customer restrictions if provided
      if customer_email && promotion_code.restrictions
        if promotion_code.restrictions.first_time_transaction && 
           existing_customer?(customer_email)
          return { valid: false, error: "This promotion is only for new customers" }
        end
      end
      
      {
        valid: true,
        coupon_id: coupon.id,
        percent_off: coupon.percent_off,
        amount_off: coupon.amount_off,
        currency: coupon.currency,
        duration: coupon.duration,
        duration_in_months: coupon.duration_in_months
      }
    end
    
    private
    
    def existing_customer?(email)
      begin
        customers = Stripe::Customer.list(email: email, limit: 1)
        customers.data.any?
      rescue Stripe::StripeError => e
        Rails.logger.error "Error checking existing customer: #{e.message}"
        false
      end
    end
  end
end