## Gemini AI Agent Instructions

### Creating Linear Issues

When creating a Linear issue, adhere to the following conventions to ensure consistency and clarity:

1.  **Title:**
    *   Start with a prefix indicating the type of work (e.g., `Bug:`, `Feature:`, `Security:`, `I18n:`).
    *   Provide a concise and descriptive summary of the task.
    *   End the title with a time estimate in brackets, representing the total expected time for implementation and testing (e.g., `[2h]`, `[8h]`).

2.  **Description:**
    *   Use Markdown for clear and structured formatting.
    *   Include the following sections:
        *   `## Problem Statement`: A clear and concise description of the issue or what is missing.
        *   `## Solution Overview`: A high-level summary of the proposed solution.
        *   `## 🔧 Implementation Steps`: A detailed, step-by-step guide on how to implement the solution. Include file paths and code snippets where appropriate.
        *   `## 🔬 Testing Requirements`: Specific instructions on how to test and verify the solution.
        *   `## 📈 Success Metrics`: Clear criteria that must be met for the issue to be considered resolved.
    *   Optionally, include `**Priority**` and `**Effort**` fields for quick reference.

3.  **Priority:**
    *   Set a priority level for the issue:
        *   **3**: High
        *   **2**: Medium
        *   **1**: Low
