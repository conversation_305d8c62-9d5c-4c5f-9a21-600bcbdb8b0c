<!-- ABOUTME: Admin index page for managing referral codes with clean, minimalist design -->
<!-- ABOUTME: Displays all referral codes with filtering and inline creation form -->

<% content_for :title, "Referral Codes" %>

<div class="modern-admin-container">
  <header class="modern-admin-header">
    <div>
      <h1>Referral Codes</h1>
      <p>Manage subscription referral codes and promotions.</p>
    </div>
    <%= link_to "Subscriptions", admin_subscriptions_path, class: "button" %>
  </header>

  <!-- Create New Code Section -->
  <div class="modern-admin-card">
    <h2 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem;">Create New Code</h2>
    
    <%= form_with model: [:admin, @new_code], local: true do |form| %>
      <% if @new_code.errors.any? %>
        <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 0.375rem; padding: 1rem; margin-bottom: 1rem;">
          <p style="color: #dc2626; font-weight: 600; margin-bottom: 0.5rem;">Please fix the following errors:</p>
          <ul style="list-style: disc; margin-left: 1.5rem; color: #dc2626;">
            <% @new_code.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem; margin-bottom: 1rem;">
        <div>
          <%= form.label :code, style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.25rem;" %>
          <%= form.text_field :code, placeholder: "e.g., WELCOME2024", style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem;" %>
        </div>
        
        <div>
          <%= form.label :tier_upgrade_to, "Upgrade To", style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.25rem;" %>
          <%= form.select :tier_upgrade_to,
              options_for_select([['Premium', 'premium'], ['Pilot', 'pilot']]),
              {},
              { style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem; background: white;" } %>
        </div>
        
        <div>
          <%= form.label :duration_months, "Duration (months)", style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.25rem;" %>
          <%= form.number_field :duration_months, value: 1, min: 1, style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem;" %>
        </div>
      </div>

      <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem; margin-bottom: 1rem;">
        <div>
          <%= form.label :discount_percentage, "Discount %", style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.25rem;" %>
          <%= form.number_field :discount_percentage, value: 100, min: 0, max: 100, step: 0.01, style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem;" %>
        </div>
        
        <div>
          <%= form.label :max_uses, "Max Uses", style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.25rem;" %>
          <%= form.number_field :max_uses, value: 1, min: 1, style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem;" %>
        </div>
        
        <div>
          <%= form.label :expires_at, "Expires At", style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.25rem;" %>
          <%= form.datetime_local_field :expires_at,
              value: 1.month.from_now.strftime("%Y-%m-%dT%H:%M"),
              style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem;" %>
        </div>
      </div>

      <div style="margin-bottom: 1rem;">
        <%= form.label :description, style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.25rem;" %>
        <%= form.text_area :description, rows: 2, placeholder: "Optional description for this code", 
            style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem; resize: vertical;" %>
      </div>

      <%= form.submit "Create Code", style: "padding: 0.5rem 1rem; background: #3b82f6; color: white; border: none; border-radius: 0.375rem; font-size: 0.875rem; font-weight: 500; cursor: pointer;" %>
    <% end %>
  </div>

  <!-- Existing Codes Section -->
  <div class="modern-admin-card" style="margin-top: 1.5rem;">
    <h2 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem;">Existing Codes</h2>
    
    <% if @codes && @codes.any? %>
      <div style="overflow-x: auto;">
        <table style="width: 100%; border-collapse: collapse;">
          <thead>
            <tr style="border-bottom: 1px solid #e5e7eb;">
              <th style="text-align: left; padding: 0.75rem 1rem; font-size: 0.75rem; font-weight: 500; color: #6b7280; text-transform: uppercase;">Code</th>
              <th style="text-align: left; padding: 0.75rem 1rem; font-size: 0.75rem; font-weight: 500; color: #6b7280; text-transform: uppercase;">Status</th>
              <th style="text-align: left; padding: 0.75rem 1rem; font-size: 0.75rem; font-weight: 500; color: #6b7280; text-transform: uppercase;">Tier</th>
              <th style="text-align: left; padding: 0.75rem 1rem; font-size: 0.75rem; font-weight: 500; color: #6b7280; text-transform: uppercase;">Usage</th>
              <th style="text-align: left; padding: 0.75rem 1rem; font-size: 0.75rem; font-weight: 500; color: #6b7280; text-transform: uppercase;">Discount</th>
              <th style="text-align: left; padding: 0.75rem 1rem; font-size: 0.75rem; font-weight: 500; color: #6b7280; text-transform: uppercase;">Expires</th>
              <th style="text-align: right; padding: 0.75rem 1rem; font-size: 0.75rem; font-weight: 500; color: #6b7280; text-transform: uppercase;">Actions</th>
            </tr>
          </thead>
          <tbody>
            <% @codes.each do |code| %>
              <tr style="border-bottom: 1px solid #f3f4f6;">
                <td style="padding: 1rem;">
                  <div style="font-weight: 600; color: #111827;"><%= code.code %></div>
                  <% if code.description.present? %>
                    <div style="font-size: 0.75rem; color: #6b7280; margin-top: 0.25rem;"><%= truncate(code.description, length: 50) %></div>
                  <% end %>
                </td>
                
                <td style="padding: 1rem;">
                  <span style="display: inline-block; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500;
                    <%= if code.status == 'active'
                      'background: #d1fae5; color: #065f46;'
                    elsif code.status == 'expired'
                      'background: #fee2e2; color: #991b1b;'
                    elsif code.status == 'used_up'
                      'background: #fef3c7; color: #92400e;'
                    else
                      'background: #f3f4f6; color: #6b7280;'
                    end %>">
                    <%= code.status.humanize %>
                  </span>
                </td>
                
                <td style="padding: 1rem;">
                  <span style="display: inline-block; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500;
                    <%= code.tier_upgrade_to == 'premium' ? 'background: #ede9fe; color: #5b21b6;' : 'background: #dbeafe; color: #1e40af;' %>">
                    <%= code.tier_upgrade_to.humanize %>
                  </span>
                </td>
                
                <td style="padding: 1rem;">
                  <div style="font-size: 0.875rem; color: #374151;">
                    <%= code.current_uses %> / <%= code.max_uses %>
                  </div>
                  <% if code.max_uses > 0 %>
                    <% percentage = (code.current_uses.to_f / code.max_uses * 100).round %>
                    <div style="width: 60px; height: 4px; background: #e5e7eb; border-radius: 2px; margin-top: 0.25rem;">
                      <div style="width: <%= [percentage, 100].min %>%; height: 100%; background: <%= percentage >= 100 ? '#dc2626' : '#3b82f6' %>; border-radius: 2px;"></div>
                    </div>
                  <% end %>
                </td>
                
                <td style="padding: 1rem;">
                  <span style="font-weight: 600; color: #059669;"><%= code.discount_percentage %>%</span>
                </td>
                
                <td style="padding: 1rem;">
                  <% if code.expires_at %>
                    <div style="font-size: 0.875rem; color: #374151;"><%= code.expires_at.strftime("%b %d, %Y") %></div>
                    <% if code.expires_at.past? %>
                      <div style="font-size: 0.75rem; color: #dc2626;">Expired</div>
                    <% else %>
                      <div style="font-size: 0.75rem; color: #6b7280;"><%= distance_of_time_in_words_to_now(code.expires_at) %> left</div>
                    <% end %>
                  <% else %>
                    <span style="font-size: 0.875rem; color: #6b7280;">Never</span>
                  <% end %>
                </td>
                
                <td style="padding: 1rem; text-align: right;">
                  <div style="display: flex; gap: 0.5rem; justify-content: flex-end;">
                    <%= link_to "View", admin_referral_code_path(code), 
                        style: "color: #6b7280; text-decoration: none; font-size: 0.875rem;" %>
                    <%= link_to "Edit", edit_admin_referral_code_path(code), 
                        style: "color: #3b82f6; text-decoration: none; font-size: 0.875rem;" %>
                    <%= button_to "Delete", admin_referral_code_path(code),
                        method: :delete,
                        form: { style: "display: inline;", onsubmit: "return confirm('Delete this code?');" },
                        style: "background: none; border: none; color: #dc2626; cursor: pointer; padding: 0; font-size: 0.875rem;" %>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
      
      <% if @pagy.pages > 1 %>
        <div style="text-align: center; margin-top: 1.5rem;">
          <%== pagy_nav(@pagy) %>
        </div>
      <% end %>
    <% else %>
      <p style="text-align: center; color: #6b7280; margin: 2rem 0;">No referral codes have been created yet.</p>
    <% end %>
  </div>
</div>