<!-- ABOUTME: Edit page for referral codes with clean, minimalist form design -->
<!-- ABOUTME: Provides form to update existing referral code settings and properties -->

<% content_for :title, "Edit Referral Code" %>

<div class="modern-admin-container">
  <header class="modern-admin-header">
    <div>
      <h1>Edit Referral Code</h1>
      <p>Update code: <%= @code.code %></p>
    </div>
    <%= link_to "Back to Codes", admin_referral_codes_path, class: "button" %>
  </header>

  <div class="modern-admin-card">
    <%= form_with model: [:admin, @code], local: true do |form| %>
      <% if @code.errors.any? %>
        <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 0.375rem; padding: 1rem; margin-bottom: 1rem;">
          <p style="color: #dc2626; font-weight: 600; margin-bottom: 0.5rem;">Please fix the following errors:</p>
          <ul style="list-style: disc; margin-left: 1.5rem; color: #dc2626;">
            <% @code.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; margin-bottom: 1.5rem;">
        <div>
          <%= form.label :code, style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;" %>
          <%= form.text_field :code, style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem;" %>
        </div>
        
        <div>
          <%= form.label :status, style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;" %>
          <%= form.select :status,
              options_for_select([
                ['Active', 'active'],
                ['Expired', 'expired'],
                ['Used Up', 'used_up'],
                ['Disabled', 'disabled']
              ], @code.status),
              {},
              { style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem; background: white;" } %>
        </div>
      </div>

      <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem; margin-bottom: 1.5rem;">
        <div>
          <%= form.label :tier_upgrade_to, "Upgrade To", style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;" %>
          <%= form.select :tier_upgrade_to,
              options_for_select([
                ['Premium', 'premium'],
                ['Pilot', 'pilot']
              ], @code.tier_upgrade_to),
              {},
              { style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem; background: white;" } %>
        </div>
        
        <div>
          <%= form.label :duration_months, "Duration (months)", style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;" %>
          <%= form.number_field :duration_months, min: 1, style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem;" %>
        </div>
        
        <div>
          <%= form.label :discount_percentage, "Discount %", style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;" %>
          <%= form.number_field :discount_percentage, min: 0, max: 100, step: 0.01, style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem;" %>
        </div>
      </div>

      <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; margin-bottom: 1.5rem;">
        <div>
          <%= form.label :max_uses, "Max Uses", style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;" %>
          <%= form.number_field :max_uses, min: 1, style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem;" %>
          <p style="font-size: 0.75rem; color: #6b7280; margin-top: 0.25rem;">Currently used: <%= @code.current_uses %> times</p>
        </div>
        
        <div>
          <%= form.label :expires_at, "Expires At", style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;" %>
          <%= form.datetime_local_field :expires_at,
              value: @code.expires_at&.strftime("%Y-%m-%dT%H:%M"),
              style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem;" %>
          <p style="font-size: 0.75rem; color: #6b7280; margin-top: 0.25rem;">Leave blank for no expiration</p>
        </div>
      </div>

      <div style="margin-bottom: 1.5rem;">
        <%= form.label :description, style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;" %>
        <%= form.text_area :description, rows: 3, 
            style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem; resize: vertical;" %>
      </div>

      <div style="display: flex; gap: 0.5rem;">
        <%= form.submit "Update Code", style: "padding: 0.5rem 1rem; background: #3b82f6; color: white; border: none; border-radius: 0.375rem; font-size: 0.875rem; font-weight: 500; cursor: pointer;" %>
        <%= link_to "Cancel", admin_referral_code_path(@code), 
            style: "padding: 0.5rem 1rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem; font-weight: 500; color: #374151; text-decoration: none; display: inline-block;" %>
      </div>
    <% end %>
  </div>
</div>