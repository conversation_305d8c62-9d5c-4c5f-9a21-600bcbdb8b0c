<!-- ABOUTME: Show page for referral code details with clean, minimalist design -->
<!-- ABOUTME: Displays complete code information and quick update form -->

<% content_for :title, "Referral Code Details" %>

<div class="modern-admin-container">
  <header class="modern-admin-header">
    <div>
      <h1>Referral Code Details</h1>
      <p>Code: <%= @code.code %></p>
    </div>
    <div style="display: flex; gap: 0.5rem;">
      <%= link_to "Edit", edit_admin_referral_code_path(@code), class: "button" %>
      <%= link_to "Back to Codes", admin_referral_codes_path, 
          style: "padding: 0.5rem 1rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem; font-weight: 500; color: #374151; text-decoration: none; display: inline-block;" %>
    </div>
  </header>

  <!-- Code Information Section -->
  <div class="modern-admin-card">
    <h2 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1.5rem;">Code Information</h2>
    
    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1.5rem;">
      <div>
        <p style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.25rem;">CODE</p>
        <p style="font-size: 1.125rem; font-weight: 600; color: #111827;"><%= @code.code %></p>
      </div>
      
      <div>
        <p style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.25rem;">STATUS</p>
        <span style="display: inline-block; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.875rem; font-weight: 500;
          <%= if @code.status == 'active'
            'background: #d1fae5; color: #065f46;'
          elsif @code.status == 'expired'
            'background: #fee2e2; color: #991b1b;'
          elsif @code.status == 'used_up'
            'background: #fef3c7; color: #92400e;'
          else
            'background: #f3f4f6; color: #6b7280;'
          end %>">
          <%= @code.status.humanize %>
        </span>
      </div>
      
      <div>
        <p style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.25rem;">UPGRADE TIER</p>
        <span style="display: inline-block; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.875rem; font-weight: 500;
          <%= @code.tier_upgrade_to == 'premium' ? 'background: #ede9fe; color: #5b21b6;' : 'background: #dbeafe; color: #1e40af;' %>">
          <%= @code.tier_upgrade_to.humanize %>
        </span>
      </div>
      
      <div>
        <p style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.25rem;">DURATION</p>
        <p style="font-size: 0.875rem; color: #374151;"><%= pluralize(@code.duration_months, 'month') %></p>
      </div>
      
      <div>
        <p style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.25rem;">DISCOUNT</p>
        <p style="font-size: 1.125rem; font-weight: 600; color: #059669;"><%= @code.discount_percentage %>%</p>
      </div>
      
      <div>
        <p style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.25rem;">USAGE</p>
        <div>
          <p style="font-size: 0.875rem; color: #374151; margin-bottom: 0.25rem;">
            <%= @code.current_uses %> / <%= @code.max_uses %>
            <span style="color: #6b7280;">(<%= (@code.current_uses.to_f / @code.max_uses * 100).round %>% used)</span>
          </p>
          <% percentage = (@code.current_uses.to_f / @code.max_uses * 100).round %>
          <div style="width: 100%; height: 6px; background: #e5e7eb; border-radius: 3px;">
            <div style="width: <%= [percentage, 100].min %>%; height: 100%; background: <%= percentage >= 100 ? '#dc2626' : '#3b82f6' %>; border-radius: 3px;"></div>
          </div>
        </div>
      </div>
      
      <div>
        <p style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.25rem;">CREATED</p>
        <p style="font-size: 0.875rem; color: #374151;">
          <%= @code.created_at.strftime("%B %d, %Y at %I:%M %p") %>
        </p>
        <p style="font-size: 0.75rem; color: #6b7280;">
          by <%= @code.created_by.email %>
        </p>
      </div>
      
      <div>
        <p style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.25rem;">EXPIRES</p>
        <% if @code.expires_at %>
          <p style="font-size: 0.875rem; color: #374151;">
            <%= @code.expires_at.strftime("%B %d, %Y at %I:%M %p") %>
          </p>
          <% if @code.expires_at.past? %>
            <p style="font-size: 0.75rem; color: #dc2626;">Expired</p>
          <% else %>
            <% days_until_expiry = (@code.expires_at.to_date - Date.current).to_i %>
            <p style="font-size: 0.75rem; color: #6b7280;"><%= pluralize(days_until_expiry, 'day') %> remaining</p>
          <% end %>
        <% else %>
          <p style="font-size: 0.875rem; color: #6b7280;">Never expires</p>
        <% end %>
      </div>
    </div>
    
    <% if @code.description.present? %>
      <div style="margin-top: 1.5rem; padding-top: 1.5rem; border-top: 1px solid #e5e7eb;">
        <p style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.5rem;">DESCRIPTION</p>
        <p style="font-size: 0.875rem; color: #374151; line-height: 1.5;"><%= simple_format(@code.description) %></p>
      </div>
    <% end %>
  </div>

  <!-- Quick Update Section -->
  <div class="modern-admin-card" style="margin-top: 1.5rem;">
    <h2 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem;">Quick Update</h2>
    
    <%= form_with model: [:admin, @code], local: true do |form| %>
      <% if @code.errors.any? %>
        <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 0.375rem; padding: 1rem; margin-bottom: 1rem;">
          <p style="color: #dc2626; font-weight: 600; margin-bottom: 0.5rem;">Please fix the following errors:</p>
          <ul style="list-style: disc; margin-left: 1.5rem; color: #dc2626;">
            <% @code.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; margin-bottom: 1rem;">
        <div>
          <%= form.label :status, style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;" %>
          <%= form.select :status,
              options_for_select([
                ['Active', 'active'],
                ['Expired', 'expired'],
                ['Used Up', 'used_up'],
                ['Disabled', 'disabled']
              ], @code.status),
              {},
              { style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem; background: white;" } %>
        </div>
        
        <div>
          <%= form.label :expires_at, "Expires At", style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;" %>
          <%= form.datetime_local_field :expires_at,
              value: @code.expires_at&.strftime("%Y-%m-%dT%H:%M"),
              style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem;" %>
        </div>
      </div>

      <div style="margin-bottom: 1rem;">
        <%= form.label :description, style: "display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;" %>
        <%= form.text_area :description, rows: 3, 
            style: "width: 100%; padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem; resize: vertical;" %>
      </div>

      <div style="display: flex; gap: 0.5rem; align-items: center;">
        <%= form.submit "Update Code", style: "padding: 0.5rem 1rem; background: #3b82f6; color: white; border: none; border-radius: 0.375rem; font-size: 0.875rem; font-weight: 500; cursor: pointer;" %>
        
        <%= button_to "Delete Code", admin_referral_code_path(@code),
            method: :delete,
            form: { style: "display: inline;", onsubmit: "return confirm('Are you sure you want to delete this code? This action cannot be undone.');" },
            style: "padding: 0.5rem 1rem; background: white; color: #dc2626; border: 1px solid #dc2626; border-radius: 0.375rem; font-size: 0.875rem; font-weight: 500; cursor: pointer;" %>
      </div>
    <% end %>
  </div>
</div>