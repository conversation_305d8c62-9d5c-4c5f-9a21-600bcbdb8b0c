<!-- Title Box -->
<div class="project-title-box">
  <!-- <PERSON>er Bar -->
  <div class="title-header-bar"></div>
  
  <!-- Project Title -->
  <div class="title-content">
    <h1><%= h(@project.summary) %></h1>
    <p class="location-text">
      <% if @project.location.present? %>
        <%= heroicon "map-pin", variant: :outline, options: { class: "icon-16" } %>
        <%= @project.location %>
      <% end %>
    </p>
  </div>
</div>

<!-- Content Grid Container -->
<div class="project-content-grid">
  <!-- Left Content Box -->
  <div class="project-content-left-box">
    <div class="mb-4">
      <h4 class="red">
        <%= t('projects.show.restricted_access', default: 'There is restricted access to this project. Request access from the owner, to see full details.') %>
      </h4>
    </div>

    <div class="project-details-grid">
      <div class="detail-item">
        <div class="detail-label"><%= t('projects.show.category', default: 'Category:') %></div>
        <div class="detail-value"><%= @project.translated_category.humanize.capitalize %> / <%= @project.translated_subcategory.humanize.capitalize %></div>
      </div>
    </div>
  </div>

  <!-- Right Content Box -->
  <div class="project-content-right-box">
    <div class="info-rows">
      <div class="info-row">
        <span class="info-label"><%= t('projects.show.project_owner', default: 'Project owner:') %></span>
        <span class="info-value"><%= @project.user.user_profile.first_name %> <%= @project.user.user_profile.last_name %></span>
      </div>
      
      <% if current_user == @project.user %>
        <div class="info-row">
          <span class="info-label"><%= t('projects.show.sharing.visibility', default: 'Visibility') %></span>
          <span class="info-value">
            <% if @project.summary_only %>
              <%= t('projects.show.sharing.title_only', default: 'Title only') %>
            <% else %>
              <%= t('projects.show.sharing.everything', default: 'Everything') %>
            <% end %>
            /
            <% if @project.semi_public %>
              <%= t('projects.show.sharing.everyone', default: 'Everyone') %>
            <% else %>
              <%= t('projects.show.sharing.my_network', default: 'My Network') %>
            <% end %>
          </span>
        </div>
      <% end %>
      
      <div class="info-row">
        <span class="info-label"><%= t('projects.show.last_update', default: 'Last update:') %></span>
        <span class="info-value"><%= @project.updated_at&.strftime("%b %d %Y at %H:%M") %></span>
      </div>
    </div>
    
    <% if current_user.can_request_access_to?(@project) %>
      <%= link_to t('projects.show.request_access', default: 'Request access'), new_connection_request_path(project_id: @project.id), class: "action-button modal-trigger" %>
    <% else %>
      <button class="action-button disabled" disabled title="<%= t('subscriptions.upgrade_tooltip', default: 'Upgrade to request access to summary-only projects') %>">
        <%= t('projects.show.request_access', default: 'Request Access') %>
        <div class="upgrade-required">
          <%= link_to t('subscriptions.upgrade_required', default: 'Upgrade to Request Access'), subscriptions_path, class: 'upgrade-link' %>
        </div>
      </button>
    <% end %>
  </div>
</div>