<h1><%= t('projects.index.title') %></h1>
<div class="projects-filter">
  <%= form_tag projects_path(locale: I18n.locale), method: :get, class: 'filter-form' do %>
    <%= hidden_field_tag :locale, I18n.locale %>

    <div class="filter-controls">
      <div class="filter-row">
        <div class="filter-group">
          <div class="filter-group">
            <%= select_tag 'project_type', 
                options_for_select(Project.translated_project_types, params[:project_type]), 
                include_blank: t('projects.index.filters.project_type_placeholder', default: 'Project Type'),   
                class: 'form-select',
                id: 'project_type_select' %>
          </div>
          <%
            # Get available categories for selected project type
            categories_options_for_select = Project.translated_categories_for(params[:project_type])
            
            # Ensure selected category is valid for the current project type
            selected_category_value = params[:category]
            if params[:project_type].present? && params[:category].present?
              valid_categories = Project::PROJECT_TYPES[params[:project_type]] || []
              unless valid_categories.include?(selected_category_value)
                selected_category_value = nil
              end
            end
          %>
          <%= select_tag 'category', 
              options_for_select(categories_options_for_select, selected_category_value), 
              include_blank: t('projects.index.filters.category_placeholder'),   
              class: 'form-select',
              id: 'category_select' %>  
          <%= select_tag 'subcategory', 
              options_for_select([], params[:subcategory]), 
              include_blank: t('projects.index.filters.subcategory_placeholder'),   
              class: 'form-select',
              id: 'subcategory_select' %>
        </div>

        <div class="filter-group">
          <%= text_field_tag :search, params[:search], 
              placeholder: t('projects.index.filters.search_placeholder'), 
              class: 'form-input' %>
        </div>

        <div class="filter-group location-group">
          <%= text_field_tag :location, params[:location], 
              placeholder: t('projects.index.filters.location_placeholder'), 
              autocomplete: 'off',
              class: 'form-input' %>
          <%= hidden_field_tag :latitude, '', id: 'latitude' %>
          <%= hidden_field_tag :longitude, '', id: 'longitude' %>
          <%= hidden_field_tag :country, '', id: 'country' %>
          <%= hidden_field_tag :search_country_code, '', id: 'search_country_code' %>
          <%= hidden_field_tag :is_country_search, 'false', id: 'is_country_search' %>
          <%= select_tag :radius,
              options_for_select([
                [t('projects.index.radius_options.km_20'), 20],
                [t('projects.index.radius_options.km_50'), 50],
                [t('projects.index.radius_options.km_100'), 100],
                [t('projects.index.radius_options.km_300'), 300]
              ], params[:radius]),
              include_blank: t('projects.index.filters.radius'),
              class: 'form-select form-select--small' %>
        </div>
      </div>

      <div class="filter-row">
        <div class="filter-group">
          <div class="toggle-group">
            <%= radio_button_tag :access, 'all', params[:access].nil? || params[:access] == 'all', 
                class: 'toggle-input', 
                id: 'access_all' %>
            <%= label_tag 'access_all', t('projects.index.filters.all'), class: 'toggle-label' %>
            
            <%= radio_button_tag :access, 'pending', params[:access] == 'pending', 
                class: 'toggle-input',
                id: 'access_pending' %>
            <%= label_tag 'access_pending', t('projects.index.filters.pending'), class: 'toggle-label' %>
            
            <%= radio_button_tag :access, 'full_details', params[:access] == 'full_details', 
                class: 'toggle-input',
                id: 'access_full' %>
            <%= label_tag 'access_full', t('projects.index.filters.full_access'), class: 'toggle-label' %>
          </div>
        </div>

        <div class="filter-actions">
          <%= submit_tag t('projects.index.filters.apply'), class: 'button' %>
          <%= link_to t('projects.index.filters.clear'), projects_path, class: 'text-link' %>
        </div>
      </div>
    </div>
  <% end %>
</div>
<div class="projects-list">
  <% if projects.present? %>
    <% projects.each do |project| %>
      <div class="project-item">
        
        <div class="project-main">
          <h3 class="project-title">
            <%= link_to project.summary, project_path(project) %>
          </h3>
          <div class="project-meta">
            <div class="project-location">
              <%= heroicon "map-pin", variant: :solid, options: { class: "icon-sm" } %>
              <span><%= project.location %></span>
            </div>
            <div class="project-author">
              <%= heroicon "user", variant: :solid, options: { class: "icon-sm" } %>
              <span><%= project.user.user_profile.first_name %> <%= project.user.user_profile.last_name %></span>
            </div>
          </div>
          <div>
            
          </div>
        </div>
        
        <div class="project-category">
          <div>
            <%= project.translated_project_type.humanize %>
          </div>
          <div>
            <%= project.translated_category.humanize %>
          </div>
          <div>
            <%= project.translated_subcategory.humanize %>
          </div>
        </div>
        
        <div class="project-actions">
          <% if current_user == project.user %>
            <div class="project-sharing">
              <%= heroicon "users", variant: :solid, options: { class: "icon-sm" } %>
              <span><%= project.summary_only ? t('projects.common.title_only') : t('projects.common.everything') %></span>
              <span><%= project.semi_public ? t('projects.common.everyone') : t('projects.common.my_network') %></span>
            </div>
            <%= link_to t('common.actions.edit'), edit_project_path(project), class: "action-link" %>
          <% else %>
            <% if project.user_has_access?(current_user) %>
              <div class="project-access">
                <%= heroicon "shield-check", variant: :solid, options: { class: "icon-sm" } %>
                <span><%= t('projects.common.full_details') %></span>
              </div>
              <%= link_to t('common.actions.view'), project, class: "action-link" %>
            <% elsif project.auth_level == 1 %>
              <div class="project-access">
                <%= heroicon "key", variant: :solid, options: { class: "icon-sm" } %>
                <span><%= t('common.actions.pending') %></span>
              </div>
              <%= button_to t('projects.index.project_item.delete_request'), connection_request_path(project.id), method: :delete, class: "action-button action-button--reject" %>
            <% else %>
              <% if current_user.can_request_access_to?(project) %>
                <%= link_to t('projects.index.project_item.request_access'), new_connection_request_path(project_id: project.id), class: "modal-trigger action-link" %>
              <% else %>
                <span class="action-link disabled" title="<%= t('subscriptions.upgrade_tooltip', default: 'Upgrade to request access to summary-only projects') %>">
                  <%= t('projects.index.project_item.request_access') %>
                </span>
              <% end %>
            <% end %>
          <% end %>
        </div>
        <div class="project-date">
          <%= l project.updated_at, format: :short_dmy %>
        </div>
      </div>
    <% end %>
  <% else %>
    <div class="no-projects">
      <p><%= t('projects.index.project_list.no_projects') %></p>
    </div>
  <% end %>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const projectTypeSelect = document.getElementById('project_type_select');
    const categorySelect = document.getElementById('category_select');
    const subcategorySelect = document.getElementById('subcategory_select');
    
    const projectTypes = <%= raw @cached_project_types.to_json %>;
    const categories = <%= raw @cached_categories.to_json %>;
    const translations = {
      projectTypes: <%= raw @cached_translations[:projectTypes].to_json %>,
      categories: <%= raw @cached_translations[:categories].to_json %>,
      subcategories: <%= raw @cached_translations[:subcategories].to_json %>,
      placeholders: {
        category: '<%= j @cached_placeholders[:category] %>',
        subcategory: '<%= j @cached_placeholders[:subcategory] %>'
      }
    };
    
    const initialCategoryParam = '<%= j params[:category].to_s %>';
    const initialSubcategoryParam = '<%= j params[:subcategory].to_s %>';
    
    projectTypeSelect.addEventListener('change', function() {
      const selectedProjectType = this.value;
      
      categorySelect.innerHTML = '<option value="">' + translations.placeholders.category + '</option>';
      subcategorySelect.innerHTML = '<option value="">' + translations.placeholders.subcategory + '</option>';
      
      if (selectedProjectType && projectTypes[selectedProjectType]) {
        projectTypes[selectedProjectType].forEach(function(category) {
          const option = document.createElement('option');
          option.value = category;
          option.text = translations.categories[category] || category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
          categorySelect.appendChild(option);
        });
      }
    });
    
    categorySelect.addEventListener('change', function() {
      const selectedCategory = this.value;
      
      subcategorySelect.innerHTML = '<option value="">' + translations.placeholders.subcategory + '</option>';
      
      if (selectedCategory && categories[selectedCategory]) {
        categories[selectedCategory].forEach(function(subcategory) {
          const option = document.createElement('option');
          option.value = subcategory;
          option.text = translations.subcategories[subcategory] || subcategory.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
          subcategorySelect.appendChild(option);
        });
      }
    });
    
    if (projectTypeSelect.value) {
      projectTypeSelect.dispatchEvent(new Event('change'));
      
      if (initialCategoryParam) {
        categorySelect.value = initialCategoryParam;
      }
      
      setTimeout(() => {
        if (categorySelect.value) {
          categorySelect.dispatchEvent(new Event('change'));
          if (initialSubcategoryParam) {
            subcategorySelect.value = initialSubcategoryParam;
          }
        }
      }, 100); 
    }
  });
</script>