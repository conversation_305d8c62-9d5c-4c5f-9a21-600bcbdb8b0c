<div class="main-box">
  <div class="form">
    <h2><%= t('.sign_up') %></h2>

    <%= form_for(resource, as: resource_name, url: registration_path(resource_name), html: { id: 'registration-form' }) do |f| %>
      <%= render "devise/shared/error_messages", resource: resource %>

      <div class="field">
        <%= f.label :email %>
        <%= f.email_field :email, autofocus: true, autocomplete: "email" %>
      </div>

      <div class="field">
        <%= f.label :password %>
        <% if @minimum_password_length %>
        <em>(<%= t('devise.shared.minimum_password_length', count: @minimum_password_length) %>)</em>
        <% end %>
        <%= f.password_field :password, autocomplete: "new-password" %>
      </div>

      <div class="field">
        <%= f.label :password_confirmation %>
        <%= f.password_field :password_confirmation, autocomplete: "new-password" %>
      </div>

      <!-- reCAPTCHA token field -->
      <!-- To enable reCAPTCHA: Set RECAPTCHA_SITE_KEY and RECAPTCHA_SECRET_KEY environment variables -->
      <input type="hidden" name="recaptcha_token" id="recaptcha_token">

      <div class="actions">
        <%= f.submit t('.sign_up'), id: 'submit-btn' %>
      </div>
    <% end %>

    <%= render "devise/shared/links" %>
  </div>
</div>

<% if Rails.application.credentials.recaptcha&.dig(:site_key).present? %>
<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('registration-form');
  const submitBtn = document.getElementById('submit-btn');
  let isSubmitting = false;
  
  if (form && submitBtn && typeof grecaptcha !== 'undefined') {
    form.addEventListener('submit', function(e) {
      e.preventDefault();
      
      // Prevent double submission
      if (isSubmitting) {
        return false;
      }
      
      isSubmitting = true;
      submitBtn.disabled = true;
      submitBtn.value = 'Processing...';
      
      grecaptcha.ready(function() {
        grecaptcha.execute('<%= Rails.application.credentials.recaptcha[:site_key] %>', {action: 'registration'}).then(function(token) {
          document.getElementById('recaptcha_token').value = token;
          form.submit();
        }).catch(function(error) {
          console.error('reCAPTCHA error:', error);
          submitBtn.disabled = false;
          submitBtn.value = 'Sign up';
          isSubmitting = false;
          alert('Security verification failed. Please try again.');
        });
      });
    });
  }
});
</script>
<% end %>
