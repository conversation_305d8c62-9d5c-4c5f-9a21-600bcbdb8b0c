<div class="devise-links">
  <%- if controller_name != 'sessions' %>
    <%= link_to t('.sign_in'), new_session_path(resource_name) %><br />
  <% end %>

  <%- if devise_mapping.registerable? && controller_name != 'registrations' %>
    <%= link_to t('.sign_up'), new_registration_path(resource_name) %><br />
  <% end %>

  <%- if devise_mapping.recoverable? && controller_name != 'passwords' && controller_name != 'registrations' %>
    <%= link_to t('.forgot_your_password'), new_password_path(resource_name) %><br />
  <% end %>

  <%- if devise_mapping.confirmable? && controller_name != 'confirmations' %>
    <%= link_to t('.didn_t_receive_confirmation_instructions'), new_confirmation_path(resource_name) %><br />
  <% end %>

  <%- if devise_mapping.lockable? && resource_class.unlock_strategy_enabled?(:email) && controller_name != 'unlocks' %>
    <%= link_to t('.didn_t_receive_unlock_instructions'), new_unlock_path(resource_name) %><br />
  <% end %>

  <%- if devise_mapping.omniauthable? %>
    <%- resource_class.omniauth_providers.each do |provider| %>
      <%= button_to t('.sign_in_with_provider', provider: OmniAuth::Utils.camelize(provider)), omniauth_authorize_path(resource_name, provider), data: { turbo: false } %><br />
    <% end %>
  <% end %>
</div>
