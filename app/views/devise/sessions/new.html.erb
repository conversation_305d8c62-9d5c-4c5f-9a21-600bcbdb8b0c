<div class="form">
    <h2><%= t('.sign_in') %></h2>

  <%= form_for(resource, as: resource_name, url: session_path(resource_name)) do |f| %>
    <div class="field">
      <%= f.label :email %>
      <%= f.email_field :email, autofocus: true, autocomplete: "email" %>
    </div>

    <div class="field">
      <%= f.label :password %>
      <%= f.password_field :password, autocomplete: "current-password" %>
    </div>

    <% if devise_mapping.rememberable? %>
      <div class="field checkbox-inline">
        <%= f.check_box :remember_me %>
        <%= f.label :remember_me %>
      </div>
    <% end %>

    <div class="actions">
      <%= f.submit t('.sign_in'), class: "button" %>
    </div>
  <% end %>

  <%= render "devise/shared/links" %>
</div>
