<div class="container mx-auto px-4 py-8">
  <div class="max-w-lg mx-auto">
    <div class="bg-white rounded-lg shadow-lg p-8 text-center">
      <!-- Loading animation -->
      <div class="mb-6">
        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 animate-pulse">
          <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
      </div>
      
      <h2 class="text-2xl font-bold text-gray-900 mb-4">
        <%= t('subscriptions.processing.title', default: 'Payment Successful!') %>
      </h2>
      
      <p class="text-gray-600 mb-6">
        <%= t('subscriptions.processing.message', 
              default: 'We are activating your subscription. This usually takes just a few seconds...') %>
      </p>
      
      <!-- Spinner -->
      <div class="flex justify-center mb-6">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
      
      <p class="text-sm text-gray-500">
        <%= t('subscriptions.processing.wait_message', 
              default: 'Please wait while we complete your subscription setup.') %>
      </p>
    </div>
  </div>
</div>

<script>
  // Poll for subscription status changes
  (function() {
    let pollCount = 0;
    const maxPolls = 30; // Poll for max 30 seconds
    const subscriptionId = <%= @subscription.id %>;
    
    function checkSubscriptionStatus() {
      pollCount++;
      
      fetch('<%= check_status_subscription_path(@subscription) %>', {
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.can_access_features) {
          // Subscription is now active, redirect to subscriptions page
          window.location.href = '<%= subscriptions_path %>';
        } else if (pollCount < maxPolls) {
          // Continue polling
          setTimeout(checkSubscriptionStatus, 1000);
        } else {
          // Max polls reached, redirect anyway
          window.location.href = '<%= subscriptions_path %>';
        }
      })
      .catch(error => {
        console.error('Error checking subscription status:', error);
        if (pollCount < maxPolls) {
          setTimeout(checkSubscriptionStatus, 1000);
        } else {
          window.location.href = '<%= subscriptions_path %>';
        }
      });
    }
    
    // Start polling after 2 seconds
    setTimeout(checkSubscriptionStatus, 2000);
  })();
</script>