# ABOUTME: Specialized mailer for bulk notifications that uses simulation in development
# ABOUTME: Inherits from NotificationMailer but overrides delivery method for bulk sends

class BulkNotificationMailer < NotificationMailer
  # Override the delivery job to use our custom bulk handler
  self.delivery_job = nil # Don't use the rate limited job for bulk simulation
  
  # Use bulk simulator in development when set
  def self.with_bulk_simulator
    if Rails.env.development?
      original_method = ActionMailer::Base.delivery_method
      ActionMailer::Base.delivery_method = :bulk_simulator
      yield
      ActionMailer::Base.delivery_method = original_method
    else
      yield
    end
  end
end