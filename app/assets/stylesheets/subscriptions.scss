// ABOUTME: Subscription page styles matching exact design from screenshot
// ABOUTME: Using existing app variables without custom modifications

@import "variables";

.subscription-container {
  background-color: transparent; 
  font-family: $base-font;
}

.subscription-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;

  h1 {
    font-size: 28px;
    font-weight: 600;
    color: $title-color;
    margin: 0;
  }
}

.plan-title {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.billing-toggle-container {
  .plan-header & {
    /* position: absolute;
    top: 10px;
    right: 10px; */
  }
}

.billing-toggle {
  display: inline-flex;
  background: #f5f5f5;
  border-radius: 20px;
  padding: 2px;
  
  // Smaller toggle when in plan header
  .plan-header & {
    .toggle-option {
      padding: 6px 12px;
      font-size: 12px;
      font-weight: 500;
      color: #666;
      background: transparent;
      border: none;
      border-radius: 18px;
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: auto;
      
      &:hover:not(.active) {
        color: #333;
      }
      
      &.active {
        background: white;
        color: $primary-color;
        box-shadow: 0 1px 3px rgba(0,0,0,0.15);
      }
    }
  }
}


// Coupon section styles
.coupon-section {
  max-width: 500px;
  margin: 0 auto 30px;
  
  .coupon-input-group {
    display: flex;
    justify-content: center;
    gap: 0;
    margin-bottom: 15px;
  }
  
  .coupon-code-input {
    padding: 10px 15px;
    font-size: 14px;
    border: 1px solid #e0e0e0;
    border-radius: 6px 0 0 6px;
    width: 250px;
    background-color: white;
    color: $text-color;
    
    &::placeholder {
      color: #bbb;
    }
    
    &:focus {
      outline: none;
      border-color: $primary-color;
    }
  }
  
  .apply-coupon-btn {
    padding: 0 25px;
    font-size: 14px;
    font-weight: 600;
    color: white;
    background-color: $primary-color;
    border: 1px solid $primary-color;
    border-left: none;
    border-radius: 0 6px 6px 0;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:hover {
      background-color: darken($primary-color, 10%);
    }
  }
}

.coupon-banner {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 12px 20px;
  text-align: center;
  margin-top: 10px;
  transition: all 0.3s ease;
  
  &.success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    
    .banner-text {
      color: #155724;
      font-weight: 600;
    }
  }
  
  &.error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    
    .banner-text {
      color: #721c24;
    }
  }
  
  .banner-text {
    font-size: 14px;
    font-weight: 500;
  }
}

.subscription-plans {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  max-width: 800px;
  margin: 0 auto;
}

.plan-card {
  background: white;
  border: 1px solid #ddd;
  border-radius: 12px;
  padding: 30px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);

  &.standard-plan {
    border-color: $primary-color;
    border-width: 2px;
  }
}

.plan-header {
  position: relative; // For absolute positioning of toggle
  text-align: left;
  margin-bottom: 20px;

  h2 {
    font-size: 28px;
    font-weight: 700;
    color: $title-color;
    margin-bottom: 8px;
  }

  .plan-tagline {
    font-size: 14px;
    color: $text-color;
    margin: 0;
    min-height: 40px;
    margin-top: 15px;
  }
}

.plan-price {
  text-align: left;
  margin-bottom: 25px;
  min-height: 40px; 

  .regular-price, .discounted-price-display {
    display: flex;
    align-items: baseline;
  }

  .currency {
    font-size: 28px;
    font-weight: 600;
    color: $title-color;
    margin-right: 4px;
    align-self: flex-start;
  }

  .amount, .free-text {
    font-size: 28px;
    font-weight: 700;
    color: $title-color;
    line-height: 1;
  }

  .period {
    font-size: 16px;
    color: $text-color;
    margin-left: 8px;
    align-self: flex-end;
    padding-bottom: 4px;
  }
  
  .discount-details {
    font-size: 12px;
    color: $text-color;
    margin-top: 4px;
    .original-price {
      text-decoration: line-through;
    }
  }
  
  // Original price shown with discount
  .original-price {
    font-family: "Inter", sans-serif;
    text-decoration: line-through;
    color: #999;
    font-size: 20px;
    margin-left: 10px;
  }
}

.plan-features {
  margin-bottom: 30px;
  flex-grow: 1;
  border-top: 1px solid #eee;
  padding-top: 10px;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  font-size: 15px;
  color: $text-color;

  .feature-icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
  }

}

.plan-action {
  margin-top: auto;
}

.maintenance-message {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px 20px;
  margin-bottom: 15px;
  text-align: center;

  p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
  }
}

.plan-button {
  width: 100%;
  padding: 15px 20px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;

  &.free-button {
    background-color: $light-gray;
    color: gray;
  }

  &.standard-button {
    background-color: $primary-color;
    color: white;
  }
  
  // Gray out both buttons when user has active subscription
  &.inactive-plan {
    background-color: #D7D7D7 !important;
    color: gray;
    cursor: not-allowed;
    opacity: 0.7;
  }

  &[disabled] {
    background-color: #e9ecef !important;
    color: #6c757d;
    cursor: not-allowed;
    opacity: 0.8;
  }
}

@media (max-width: 768px) {
  .subscription-header {
    flex-direction: column;
    h1 {
      margin-bottom: 20px;
    }
  }
  .subscription-plans {
    grid-template-columns: 1fr;
  }
  .subscription-container {
    margin: 20px auto;
    padding: 20px 15px;
  }
}

.upgrade-required, .upgrade-link {
  color: #ff9800 !important;
  font-weight: 500;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}