$breakpoint-mobile: 1080px;

.sharing-sidebar {
  width: 18rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  
  // Mobile responsive
  @media (max-width: $breakpoint-mobile) {
    width: 100%;
    margin: 0;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
}

.sharing-header {
  margin-bottom: 1rem;
}

.sharing-header-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sharing-icon {
  width: 1rem;
  height: 1rem;
  color: #6b7280;
}

.sharing-header h3 {
  font-weight: 600;
  color: #111827;
  margin: 0;
  font-size: 1rem;
}

.security-indicator {
  line-height: 1.25;
  margin-bottom: 1rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid;
  transition: all 0.2s;
}

.security-indicator.security-very_high {
  background-color: #fef2f2;
  border-color: #fecaca;
  
}

.security-indicator.security-high {
  background-color: #fffbeb;
  border-color: #fde68a;
}

.security-indicator.security-medium {
  background-color: #fffbeb;
  border-color: #fde68a;
}

.security-indicator.security-low {
  background-color: #f0fdf4;
  border-color: #bbf7d0;
}

.security-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.security-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
}

.security-very_high .security-dot {
  background-color: #ef4444;
}

.security-high .security-dot {
  background-color: #f59e0b;
}

.security-medium .security-dot {
  background-color: #f59e0b;
}

.security-low .security-dot {
  background-color: #10b981;
}

// Draft Status Indicators
.draft-status-bar {
  margin-bottom: 1rem;
  
  .status-indicator {
    line-height: 1.25;
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid;
    transition: all 0.2s;
    
    &.draft {
      background-color: #fffbeb;
      border-color: #fcd34d;
    }
    
    &.pending {
      background-color: #fffbeb;
      border-color: #fde68a;
    }
    
    &.published {
      background-color: #f0fdf4;
      border-color: #bbf7d0;
    }
  }
}

.status-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.status-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
}

.draft .status-dot {
  background-color: #f59e0b;
}

.pending .status-dot {
  background-color: #f59e0b;
}

.published .status-dot {
  background-color: #10b981;
}

.status-level-text {
  font-size: 0.875rem;
  font-weight: 500;
}

.draft .status-level-text {
  color: #b45309;
}

.pending .status-level-text {
  color: #b45309;
}

.published .status-level-text {
  color: #065f46;
}

.status-description {
  font-size: 0.75rem;
}

.draft .status-description {
  color: #b45309;
}

.pending .status-description {
  color: #b45309;
}

.published .status-description {
  color: #047857;
}

// Autosave Indicators
.autosave-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  z-index: 1000;
  transition: all 0.3s ease;
  border: 1px solid;
  
  &.unsaved {
    background: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
  }
  
  &.saving {
    background: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
  }
  
  &.saved {
    background: #d1ecf1;
    color: #0c5460;
    border-color: #bee5eb;
  }
  
  &.error {
    background: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
  }
  
  &.offline {
    background: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
  }
}

// Draft Actions
.draft-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;
  
  
  .status-note {
    font-size: 0.875rem;
    color: #6b7280;
    font-style: italic;
  }
}

.security-level-text {
  font-size: 0.875rem;
  font-weight: 500;
}

.security-very_high .security-level-text {
  color: #991b1b;
}

.security-high .security-level-text {
  color: #92400e;
}

.security-medium .security-level-text {
  color: #92400e;
}

.security-low .security-level-text {
  color: #065f46;
}

.security-description {
  font-size: 0.75rem;
}

.security-very_high .security-description {
  color: #dc2626;
}

.security-high .security-description {
  color: #b45309;
}

.security-medium .security-description {
  color: #b45309;
}

.security-low .security-description {
  color: #047857;
}

.selection-group {
  margin-bottom: 1rem;
}

.selection-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.75rem;
}

.selection-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
  
  // Mobile responsive
  @media (max-width: $breakpoint-mobile) {
    gap: 0.75rem;
  }
}

.selection-button {
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 4rem;
  
  // Mobile responsive
  @media (max-width: $breakpoint-mobile) {
    padding: 1rem;
    min-height: 4.5rem;
  }
}

.selection-button:hover {
  border-color: #d1d5db;
}

.selection-button.active {
  border-color: #2271B1;
  background-color: #2271b115;
}

.selection-button.disabled {
  cursor: not-allowed;
  background: #f9f9f9;
  .selection-text {
    color: gray;
  }
}

.selection-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin: 0 auto 0.5rem;
  color: #6b7280;
}

.selection-button.active .selection-icon {
  color: #2271B1;
}

.selection-text {
  font-size: 0.75rem;
  font-weight: 500;
  color: #111827;
}

.selection-subtext {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.project-status-section {
  padding-top: 1rem;
}

.project-status-section .mb-1 {
  margin-bottom: 0.75rem;
}

.project-status-section .checkbox {
  margin-top: 0.5rem;
}

// My Projects Index styles
.awaiting-approval {
  margin-top: 0.5rem;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0.375rem;
}

.status-badge.pending {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fbbf24;
}