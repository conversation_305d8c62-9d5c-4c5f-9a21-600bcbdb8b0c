namespace :subscriptions do
  desc "Manually creates a 'Standard Yearly' subscription for all existing users, bypassing validations."
  task create_manual_yearly: :environment do
    plan_name = 'Standard Yearly'
    plan = Plan.find_by(name: plan_name)

    unless plan
      puts "Error: Plan '#{plan_name}' not found. Please ensure the plan exists before running this task."
      next
    end

    puts "Found plan: #{plan.name} (ID: #{plan.id})"
    puts "Starting subscription creation for all users..."

    User.find_each do |user|
      # Check if the user already has an active subscription to this plan
      if user.subscriptions.exists?(plan_id: plan.id, status: :active)
        puts "User ##{user.id} (#{user.email}) already has an active subscription to '#{plan_name}'. Skipping."
        next
      end

      subscription = user.subscriptions.new(
        plan: plan,
        status: :active,
        payment_provider: :manual,
        current_period_start: Time.current,
        current_period_end: 1.year.from_now
      )

      if subscription.save(validate: false)
        puts "Successfully created manual subscription for User ##{user.id} (#{user.email})."
      else
        puts "Failed to create subscription for User ##{user.id} (#{user.email}). Errors: #{subscription.errors.full_messages.join(', ')}"
      end
    end

    puts "Subscription creation process finished."
  end
end
