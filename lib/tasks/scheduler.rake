# ABOUTME: Rake tasks for scheduled jobs that are triggered by external cron services
# ABOUTME: Provides entry points for Render cron jobs to execute periodic background tasks
namespace :scheduler do
  desc "Send daily digest email with count of new projects to eligible users"
  task send_daily_digest: :environment do
    puts "[#{Time.current}] Starting daily digest task..."
    
    # Enqueue the background job
    DailyNewProjectsDigestJob.perform_later
    
    puts "[#{Time.current}] Daily digest job enqueued successfully"
  end
  
  desc "Send weekly digest email with count of new projects to eligible users"
  task send_weekly_digest: :environment do
    puts "[#{Time.current}] Starting weekly digest task..."
    
    # Enqueue the background job
    WeeklyNewProjectsDigestJob.perform_later
    
    puts "[#{Time.current}] Weekly digest job enqueued successfully"
  end
end