# ABOUTME: Custom mail delivery method for simulating bulk email sends in development
# ABOUTME: Collects statistics and logs summary instead of individual email content

class BulkMailSimulator
  attr_reader :stats, :settings

  def initialize(settings = {})
    @settings = settings
    reset_stats!
  end

  # This method is required for Rails delivery methods
  def merge(settings)
    @settings.merge!(settings)
    self
  end

  def deliver!(mail)
    # Track the delivery attempt
    @stats[:total] += 1
    @stats[:recipients] += Array(mail.to).size
    
    # Simulate potential failures (configurable failure rate)
    failure_rate = @settings[:failure_rate] || 0.0
    if rand < failure_rate
      @stats[:failed] += 1
      @stats[:errors] << {
        to: mail.to,
        subject: mail.subject,
        error: "Simulated delivery failure"
      }
    else
      @stats[:successful] += 1
    end
    
    # Track by recipient domain for analysis
    Array(mail.to).each do |email|
      domain = email.split('@').last
      @stats[:by_domain][domain] ||= 0
      @stats[:by_domain][domain] += 1
    end
    
    # Don't output individual email content
    # Just return the mail object as if delivered
    mail
  end

  def reset_stats!
    @stats = {
      total: 0,
      successful: 0,
      failed: 0,
      recipients: 0,
      errors: [],
      by_domain: {},
      started_at: Time.current
    }
  end

  def log_summary
    duration = Time.current - @stats[:started_at]
    
    Rails.logger.info "=" * 60
    Rails.logger.info "📧 BULK MAIL SIMULATION COMPLETE"
    Rails.logger.info "=" * 60
    Rails.logger.info "Duration: #{duration.round(2)} seconds"
    Rails.logger.info "Total Emails: #{@stats[:total]}"
    Rails.logger.info "✅ Successful: #{@stats[:successful]}"
    Rails.logger.info "❌ Failed: #{@stats[:failed]}"
    Rails.logger.info "Recipients: #{@stats[:recipients]}"
    
    if @stats[:by_domain].any?
      Rails.logger.info "-" * 40
      Rails.logger.info "By Domain:"
      @stats[:by_domain].each do |domain, count|
        Rails.logger.info "  #{domain}: #{count}"
      end
    end
    
    if @stats[:errors].any?
      Rails.logger.info "-" * 40
      Rails.logger.info "Errors:"
      @stats[:errors].each do |error|
        Rails.logger.info "  ❌ #{error[:to].join(', ')}: #{error[:error]}"
      end
    end
    
    Rails.logger.info "=" * 60
  end
end