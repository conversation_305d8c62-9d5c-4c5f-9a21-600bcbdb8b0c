# CLAUDE.md

This file provides MUST DO OR MUST CHECK OR MUST INVESTIGATE OR MUST FOLLOW GUIDES OR **instructions** to <PERSON> Code AGENT when working with code in this repository.

Claude Code Agent is **FORBIDDEN** to include any reference on the tool usage in commit messages, in the PR messages or any other git, github, or Linear messages, issues, projects. 

**Claude Code Agent MUST FIRST CONSIDER ALL USER'S INTERACTIONS AS MUST DO INSTRUCTIONS AND NOT AS RECOMMENDATIONS, UNLESS EXPLICITLY STATED.** 


1. You must start a file name with current date in the format YYYY-MM-DD, for example e.g. 2025-09-05 when creating a documentation .md file
2. Server runs at ************:5000 Check before starting your own. 
3. Server runs with foreman start -f Procfile.dev  
4. We do not build npm - we use Vite and HMR
5. We do use classic javascript for now, no turbo, no vue
6. You must use the context7 first to check the official docs, only if nothing there, use other methods
7. You are forbidden to edit migration files or schema.rb
8. You must create new migrations by running `rails generate migration`



## Application Overview

Ruby on Rails 7.0.8 application called "Unlisters App" - a platform for listing and sharing private investment opportunities and projects. Users create profiles, connect through invitations, and share project listings with controlled access levels.

## Key Technologies & Dependencies

- **Ruby**: 3.1.2 | **Rails**: 7.0.8+ | **Database**: PostgreSQL
- **Frontend**: Vite with Vue.js 3 components, SCSS styling
- **Authentication**: Devise with invitation system (devise_invitable)
- **Authorization**: ActionPolicy | **File Storage**: Active Storage with AWS S3
- **Background Jobs**: GoodJob with PostgreSQL | **Testing**: RSpec
- **Email**: Resend | **Geocoding**: Geocoder gem
- **Internationalization**: Slovak (sk) default, English (en) fallback

## MANDATORY RAILS-FIRST CONSTRAINT

**Before suggesting ANY external gems, libraries, or system dependencies:**
1. FIRST: Check if Rails framework has a built-in solution
2. FIRST: Check if Active Record, Active Storage, Active Job, etc. provide the needed functionality  
3. FIRST: Verify the official Rails guides and API documentation
4. ONLY if Rails truly lacks the capability, then suggest external solutions

EXPLICITLY state: "Rails built-in check: [feature] IS/IS NOT available in framework"

**Default assumption**: Rails probably has it built-in. Prove it doesn't before adding dependencies.

## Core Domain Models

- **User**: Devise-based with invitation system, roles (regular/admin/super_boss), network connections
- **Project**: Main entity with complex project type/category/subcategory enums, geo-location, privacy levels, approval workflow
- **NetworkConnection**: Bidirectional user connections created through invitations
- **ConnectionRequest**: Requests for network connections or project access
- **ProjectAuth**: Authorization levels for project access (summary vs full details)
- **UserProfile**: Extended user information with location data
- **Upload**: Server-side file upload tracking with Ready Flag pattern

## TEST DRIVEN DEVELOPMENT

**MANDATORY APPROACH**: Always use Test Driven Development (TDD) methodology:

1. **RED**: Write a failing test first that describes the desired behavior
2. **GREEN**: Write the minimal code to make the test pass
3. **REFACTOR**: Improve the code while keeping tests green

**TDD Workflow**:
```bash
# 1. Write failing test
bundle exec rspec spec/path/to/new_feature_spec.rb
# Should fail with clear error message

# 2. Write minimal implementation
# Edit the actual code files

# 3. Run test again - should pass
bundle exec rspec spec/path/to/new_feature_spec.rb

# 4. Run full test suite to ensure no regressions
bundle exec rspec

# 5. Refactor if needed, keeping tests green
```

**Test Structure**: Follow existing patterns in spec/ directory. Use RSpec best practices:
- Descriptive test names
- Arrange-Act-Assert pattern
- Factory-based test data (if factories exist)
- Mocking external services

**NEVER** implement features before writing tests. **ALWAYS** verify tests fail before implementing.

## Writing Code

- **Focus**: NEVER make code changes unrelated to your current task. Document unrelated issues separately.
- **Comments**: NEVER remove code comments unless they are actively false. Comments are important documentation.
- **File Headers**: All code files should start with brief 2-line comment: each line starts with "ABOUTME: "
- **Comment Style**: Write evergreen comments describing what code does, not how it evolved.

## Development Commands


### Database
```bash
bin/rails db:create db:migrate db:seed
```

### Testing (TDD Essential Commands)
```bash
bundle exec rspec                           # Run all tests
bundle exec rspec spec/models/              # Run model tests
bundle exec rspec spec/path/to/file_spec.rb # Run specific test
bundle exec rspec spec/requests/*security*  # Run security tests

# Clean test database if needed
RAILS_ENV=test bundle exec rails db:reset db:migrate
```

### Console & Background Jobs
```bash
bin/rails console         # Rails console
bin/rails console -s      # Sandbox mode
bundle exec good_job start # Start job worker (production)
```

## Application Architecture Overview

**Detailed documentation**: See [`docs/architecture/`](./docs/architecture/) for comprehensive architectural details.

### Key Architectural Patterns
- **Invitation-only registration** (ENV['INVITE_ONLY'] controls)
- **Bidirectional network connections** (auto-created on invitation acceptance)
- **Two-dimensional project sharing**: Visibility (network_only/semi_public) + Access Level (summary_only/full_access)
- **Admin approval workflow** for projects
- **ActionPolicy-based authorization**

### Project Sharing System ✅
Projects implement sophisticated privacy controls with automatic access based on sharing preferences.

**Current Status**: UI sharing preferences work exactly as users expect - connected users automatically see full details when "Share Everything" + "My Network" is selected.

**Implementation**: See [`docs/features/project-sharing/`](./docs/features/project-sharing/) for complete details.

### Security Architecture
- **Secure file access system** with dual-path architecture
- **Authorization controls** via ActionPolicy
- **Admin approval workflows** for content moderation

**Security Documentation**: 
- [`docs/security/THREAT_MODEL.md`](./docs/security/THREAT_MODEL.md) - Technical security controls
- [`docs/security/PLAYBOOK.md`](./docs/security/PLAYBOOK.md) - Incident response procedures

### File System
Server-side upload system with Ready Flag pattern, AWS S3 storage, Lambda thumbnail generation.

**File System Documentation**: [`docs/features/file-system/`](./docs/features/file-system/) - Complete file system architecture and implementation details.

## Common Development Patterns

### Authorization Checks
```ruby
authorize! user_profile, to: :show?
```

### Project Access Control
Projects have two-dimensional access:
- **Visibility**: `network_only` vs `semi_public` 
- **Detail Level**: `summary_only` vs `full_access`

### Background Jobs
Uses GoodJob with PostgreSQL. Admin dashboard at `/good_job`. Email notifications primary use case.

## Troubleshooting

**Comprehensive troubleshooting**: See [`docs/maintenance/`](./docs/maintenance/) for detailed troubleshooting guides.

### Quick Fixes
- **Memory issues**: Use `/memory_debug/stats` endpoint
- **Geocoding problems**: Check `config/initializers/geocoder.rb`
- **Upload issues**: Check upload status and stuck upload cleanup commands

## NOTIFICATION PROTOCOL:
IMPORTANT: Before any state where you are awaiting confirmation, awaiting input, awaiting decision, requesting permission, or waiting for review:
1. Send email via Gmail MCP with:
   - Subject: "Claude Code: [STATE] - [BRIEF_CONTEXT]"
   - Body: "Action required in terminal. Check your Claude Code session."
2. Then display the full details and request in the terminal as normal
3. Do not include command details or sensitive information in the email
4. Keep the email brief - just a notification that attention is needed
EXAMPLES:
- Subject: "Claude Code: Awaiting Confirmation - File Deletion"
- Subject: "Claude Code: Awaiting Input - Database Connection"
- Subject: "Claude Code: Requesting Permission - System Modification" 


## Documentation References

**Detailed Technical Guides**:
- [`docs/architecture/`](./docs/architecture/) - Application architecture, non-obvious decisions, complex queries
- [`docs/features/`](./docs/features/) - Feature-specific implementation details
- [`docs/security/`](./docs/security/) - Security architecture and procedures
- [`docs/maintenance/`](./docs/maintenance/) - Troubleshooting, known issues, maintenance procedures

**Specialized Guides**:
- [`AR_QUERY_GUIDE.md`](./AR_QUERY_GUIDE.md) - Advanced ActiveRecord querying patterns
- [`BACKGROUND_JOBS_GUIDE.md`](./BACKGROUND_JOBS_GUIDE.md) - Background jobs system with GoodJob
- [`FILES_STORAGE_TRANSITION.md`](./FILES_STORAGE_TRANSITION.md) - File storage architecture details

## Quick Reference

**Security Testing**: `bundle exec rspec spec/requests/*security*`
**Memory Debug**: `/memory_debug/stats` endpoint  
**Job Dashboard**: `/good_job` (admin access)
**Default Locale**: Slovak (:sk) with English (:en) fallback
**Time Zone**: CET
- when asked to investigate act like a detective investigating the actual facts and do not act like a security consultant making dramatic claims
- You are not allowed to include coding tools like Claude Code in commit, in PR nor any other messages.