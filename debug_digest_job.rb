# Debug script for DailyNewProjectsDigestJob
puts "🔍 Debugging DailyNewProjectsDigestJob"

# Load FactoryBot
FactoryBot.reload

# Create test users using factories
project_owner = FactoryBot.create(:user, approved: true)
network_user = FactoryBot.create(:user, approved: true) 
admin_user = FactoryBot.create(:user, role: :super_boss, approved: true)

# Create profiles
[project_owner, network_user].each do |user|
  FactoryBot.create(:user_profile, user: user)
end

puts "✓ Created users: project_owner(#{project_owner.id}), network_user(#{network_user.id}), admin(#{admin_user.id})"

# Create network connection
connection = NetworkConnection.create!(inviter: project_owner, invitee: network_user, is_accepted: true)
puts "✓ Created network connection: #{connection.id} (accepted: #{connection.is_accepted})"

# Test the user network query
network_query = User.joins(
  "INNER JOIN network_connections ON 
   (network_connections.inviter_id = users.id AND network_connections.invitee_id = #{project_owner.id})
   OR 
   (network_connections.invitee_id = users.id AND network_connections.inviter_id = #{project_owner.id})"
).where("network_connections.is_accepted = ?", true)
 .active
 .where(approved: true)

puts "📊 Network query found #{network_query.count} users: #{network_query.pluck(:id)}"

# Create project using factory
FactoryBot.reload
project = FactoryBot.create(:project, 
  user: project_owner,
  network_only: true,
  semi_public: false,
  created_at: 1.hour.ago,
  project_status: true,
  approved: true,
  admin_approver: admin_user,
  is_admin_approval_action: true
)

puts "✓ Created project: #{project.id} (approved: #{project.approved?}, network_only: #{project.network_only?})"

# Test the project query used in the job
new_projects = Project.where(
  created_at: 24.hours.ago..Time.current,
  approved: true,
  project_status: true
)

puts "📊 Job query found #{new_projects.count} new projects"

# Test the full job logic manually
users_to_notify = Set.new

new_projects.each do |p|
  if p.network_only?
    accepted_connection_user_ids = User.joins(
      "INNER JOIN network_connections ON 
       (network_connections.inviter_id = users.id AND network_connections.invitee_id = #{p.user.id})
       OR 
       (network_connections.invitee_id = users.id AND network_connections.inviter_id = #{p.user.id})"
    ).where("network_connections.is_accepted = ?", true)
     .active
     .where(approved: true)
     .pluck(:id)
    
    users_to_notify.merge(accepted_connection_user_ids)
    puts "📊 Project #{p.id} (network_only) - adding #{accepted_connection_user_ids.count} network users: #{accepted_connection_user_ids}"
  end
end

puts "📊 Total unique users to notify: #{users_to_notify.size} - #{users_to_notify.to_a}"

# Get eligible users
eligible_users = User.where(id: users_to_notify.to_a)
                     .active
                     .where(approved: true)
                     .includes(:user_profile)

puts "📊 Eligible users to send emails: #{eligible_users.count}"
eligible_users.each do |user|
  puts "  - User #{user.id}: #{user.email} (approved: #{user.approved?})"
end

# Test actual job execution
puts "\n🚀 Running actual job..."
DailyNewProjectsDigestJob.perform_now

puts "\n✅ Debug complete!"