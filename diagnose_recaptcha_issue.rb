#!/usr/bin/env ruby
# ABOUTME: Comprehensive diagnosis of reCAPTCHA configuration issues
# ABOUTME: Identifies the exact problem preventing registration

puts "=== RECAPTCHA DIAGNOSIS ==="
puts "\nYour Configuration:"
puts "Site Key: 6LdE8aYrAAAAADuskrEwdLvKwcVPth_rKaRtvYt8"
puts "Secret Key: 6LdE8aYr...PJaCbzWQ (hidden for security)"

puts "\n=== MOST LIKELY ISSUES (in order) ==="

puts "\n1. 🌐 DOMAIN NOT REGISTERED (Most Common)"
puts "   Go to: https://www.google.com/recaptcha/admin"
puts "   Find your key and check if 'app.unlisters.com' is in the domains list"
puts "   If missing, add it and save"

puts "\n2. 📊 RECAPTCHA VERSION MISMATCH"
puts "   Your code expects reCAPTCHA v3 (checks for 'score')"
puts "   Line 55 in controller: result['success'] && result['score'] && result['score'] >= 0.5"
puts "   If you have v2, there's no score field!"
puts "   Check in admin console what version you're using"

puts "\n3. 🔢 SCORE TOO LOW"
puts "   reCAPTCHA v3 scores users 0.0 (bot) to 1.0 (human)"
puts "   Your code requires >= 0.5"
puts "   Real users might score lower (VPN users, privacy tools, etc.)"

puts "\n4. 🔑 CREDENTIALS NOT LOADED IN PRODUCTION"
puts "   The credentials might not be properly deployed"
puts "   On production server, run:"
puts "   RAILS_ENV=production rails console"
puts "   Rails.application.credentials.recaptcha[:secret_key]"

puts "\n=== IMMEDIATE FIX OPTIONS ==="

puts "\n Option A: Disable score check (if using v3)"
puts "   Change line 55 in registrations_controller.rb to:"
puts "   success = result['success']  # Remove score check"

puts "\n Option B: Handle v2 reCAPTCHA"
puts "   Change line 55 to:"
puts "   success = result['success'] && (result['score'].nil? || result['score'] >= 0.5)"

puts "\n Option C: Lower score threshold"
puts "   Change line 55 to:"
puts "   success = result['success'] && (!result['score'] || result['score'] >= 0.3)"

puts "\n Option D: Temporary bypass for testing"
puts "   Add to line 24 in controller:"
puts "   return true if Rails.env.production? && params.dig(:user, :email) == '<EMAIL>'"

puts "\n=== TO CONFIRM THE EXACT ISSUE ==="
puts "Deploy the enhanced logging version and check production logs."
puts "The logs will show exactly why verification is failing."