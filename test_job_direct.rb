# Direct test of DailyNewProjectsDigest<PERSON><PERSON>
puts "🔍 Testing DailyNewProjectsDigestJob directly"

# Clean slate
User.destroy_all
NetworkConnection.destroy_all
Project.destroy_all

# Create users manually with confirmed_at and invitation_accepted_at
project_owner = User.create!(
  email: "<EMAIL>", 
  password: "password123", 
  approved: true, 
  confirmed_at: Time.current,
  invitation_accepted_at: Time.current
)

network_user = User.create!(
  email: "<EMAIL>", 
  password: "password123", 
  approved: true, 
  confirmed_at: Time.current,
  invitation_accepted_at: Time.current
)

admin_user = User.create!(
  email: "<EMAIL>", 
  password: "password123", 
  role: :super_boss, 
  approved: true, 
  confirmed_at: Time.current,
  invitation_accepted_at: Time.current
)

puts "✓ Created users: project_owner(#{project_owner.id}), network_user(#{network_user.id}), admin(#{admin_user.id})"
puts "Project owner active?: #{User.active.exists?(project_owner.id)}"
puts "Network user active?: #{User.active.exists?(network_user.id)}"

# Create profiles
[project_owner, network_user].each do |user|
  UserProfile.create!(user: user, first_name: "Test", last_name: "User")
end

# Create network connection
connection = NetworkConnection.create!(inviter: project_owner, invitee: network_user, is_accepted: true)
puts "✓ Created network connection: #{connection.id} (accepted: #{connection.is_accepted})"

# Test network query
query = User.joins(
  "INNER JOIN network_connections ON 
   ((network_connections.inviter_id = users.id AND network_connections.invitee_id = #{project_owner.id}) OR
    (network_connections.invitee_id = users.id AND network_connections.inviter_id = #{project_owner.id})) AND
   network_connections.is_accepted = true"
).active.where(approved: true)

puts "Network query found: #{query.count} users: #{query.pluck(:id)}"

# Create project using factory
FactoryBot.reload
project = FactoryBot.create(:project,
  user: project_owner,
  network_only: true,
  semi_public: false,
  created_at: 1.hour.ago,
  project_status: true,
  approved: true,
  admin_approver: admin_user,
  is_admin_approval_action: true
)
puts "✓ Created project: #{project.id}"

# Test job query
new_projects = Project.where(
  created_at: 24.hours.ago..Time.current,
  approved: true,
  project_status: true
)

puts "Job found #{new_projects.count} new projects"

# Test the job logic step by step
puts "\n🔧 Testing job logic step by step..."
users_to_notify = Set.new

new_projects.each do |p|
  if p.network_only?
    accepted_connection_user_ids = User.joins(
      "INNER JOIN network_connections ON 
       ((network_connections.inviter_id = users.id AND network_connections.invitee_id = #{p.user.id}) OR
        (network_connections.invitee_id = users.id AND network_connections.inviter_id = #{p.user.id})) AND
       network_connections.is_accepted = true"
    ).active
     .where(approved: true)
     .pluck(:id)
    
    users_to_notify.merge(accepted_connection_user_ids)
    puts "📊 Project #{p.id} (network_only) - adding #{accepted_connection_user_ids.count} network users: #{accepted_connection_user_ids}"
  end
end

puts "📊 Total unique users to notify: #{users_to_notify.size} - #{users_to_notify.to_a}"

# Get eligible users
eligible_users = User.where(id: users_to_notify.to_a)
                     .active
                     .where(approved: true)
                     .includes(:user_profile)

puts "📊 Eligible users to send emails: #{eligible_users.count}"

# Run the actual job
puts "\n🚀 Running DailyNewProjectsDigestJob..."
begin
  DailyNewProjectsDigestJob.perform_now
  puts "Job completed successfully!"
rescue => e
  puts "Job failed: #{e.message}"
  puts e.backtrace.first(5)
end