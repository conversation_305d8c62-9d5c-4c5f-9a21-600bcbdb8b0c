# ABOUTME: Stripe configuration initializer with test/live mode switching
# ABOUTME: Configures Stripe SDK with environment-aware credentials and safety checks
require 'stripe'

# Determine Stripe mode (defaults to test for safety)
STRIPE_MODE = (ENV['STRIPE_MODE'] || 'test').downcase
raise "Invalid STRIPE_MODE: #{STRIPE_MODE}. Must be 'test' or 'live'" unless %w[test live].include?(STRIPE_MODE)

# Log warning in production with live mode
if Rails.env.production? && STRIPE_MODE == 'live'
  Rails.logger.warn "⚠️  STRIPE LIVE MODE ACTIVE - Real payments will be processed!"
end

# Select appropriate credentials based on mode
stripe_config = Rails.application.credentials.dig(:stripe, STRIPE_MODE.to_sym)

# Set Stripe API key from credentials ONLY
Stripe.api_key = stripe_config&.dig(:secret_key)

# Set Stripe API version for consistent behavior
Stripe.api_version = '2023-10-16'

# Configure Stripe logging in development
if Rails.env.development?
  Stripe.log_level = Stripe::LEVEL_INFO
end

# Set app info for Stripe dashboard
Stripe.set_app_info(
  'Unlisters',
  version: '1.0.0',
  url: 'https://app.unlisters.com'
)

# Store webhook secret for current mode
# Use production webhook secret if in production, otherwise use local
STRIPE_WEBHOOK_SECRET = if Rails.env.production?
  # In production, use production_test secret for test mode or production secret for live mode
  stripe_config&.dig(:webhook_secrets, :production_test) || 
  stripe_config&.dig(:webhook_secret) # Fallback for legacy format
else
  # In development/test, use local secret
  stripe_config&.dig(:webhook_secrets, :local) || 
  stripe_config&.dig(:webhook_secret) # Fallback for legacy format
end

# Store publishable key for frontend
STRIPE_PUBLISHABLE_KEY = stripe_config&.dig(:publishable_key)

# Verify configuration
if Stripe.api_key.blank? && !Rails.env.test?
  Rails.logger.error "STRIPE API KEY NOT CONFIGURED for #{STRIPE_MODE} mode!"
  Rails.logger.error "Please set stripe.#{STRIPE_MODE}.secret_key in credentials"
  # Don't raise in development to allow app to start, but warn clearly
  if Rails.env.production?
    raise "Stripe #{STRIPE_MODE} API key not configured. Cannot start application without payment processing."
  end
elsif Rails.env.development? || Rails.env.production?
  begin
    # Test the connection by retrieving the account
    account = Stripe::Account.retrieve
    Rails.logger.info "✅ Stripe configured successfully in #{STRIPE_MODE.upcase} mode for account: #{account.id}"
    Rails.logger.info "   Mode: #{STRIPE_MODE.upcase} | Webhook Secret: #{STRIPE_WEBHOOK_SECRET.present? ? 'Configured' : 'Missing'}"
  rescue Stripe::AuthenticationError => e
    Rails.logger.error "Stripe authentication failed in #{STRIPE_MODE} mode: #{e.message}"
  rescue => e
    Rails.logger.error "Stripe configuration error: #{e.message}"
  end
end