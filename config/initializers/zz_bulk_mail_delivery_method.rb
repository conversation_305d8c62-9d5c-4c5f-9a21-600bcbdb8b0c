# ABOUTME: Register bulk mail simulator as a custom delivery method in Rails
# ABOUTME: Allows switching between normal and simulated delivery for bulk emails

# Named with zz_ prefix to ensure it loads after other initializers
if Rails.env.development?
  Rails.application.config.after_initialize do
    require Rails.root.join('lib', 'bulk_mail_simulator')
    
    # Register the bulk mail simulator as a delivery method
    ActionMailer::Base.add_delivery_method :bulk_simulator, BulkMailSimulator
  end
end