# config/initializers/development_email_logger.rb
class DevelopmentEmailLogger
  # Thread-local storage for bulk mode flag
  class << self
    def bulk_mode?
      Thread.current[:bulk_email_mode] || false
    end
    
    def bulk_mode=(value)
      Thread.current[:bulk_email_mode] = value
    end
  end
  
  def self.delivering_email(message)
    # Skip output if we're in bulk mode
    return if bulk_mode?
    
    body_content = if message.multipart? && message.text_part
                     message.text_part.body.decoded
                   else
                     message.body.decoded
                   end
    # Puts the decoded body directly to the log, nothing else.
    puts body_content
  end
end