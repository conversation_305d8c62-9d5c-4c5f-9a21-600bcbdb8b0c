en:
  time:
    formats:
      short_dmy: "%b %d '%y"
  recaptcha:
    verification_failed: "reCAPTCHA verification failed. Please try again."
    token_blank: "Security verification failed. Please refresh the page and try again."
    timeout_or_duplicate: "This form has already been submitted. Please refresh the page and try again."
  common:
    actions:
      edit: "Edit"
      view: "View"
      delete: "Delete"
      logout: "Logout"
      save: "Save"
      cancel: "Cancel"
      accept: "Accept"
      reject: "Reject"
      show_more: "Show more"
      grant_project_access: "Grant Deal Access"
      send: "Send"
      write_a_short_message: "Write a short message"
      pending: "pending"
      updated_successfully: "Updated successfully"
      created_successfully: "Deal created successfully."
      project_deleted: "Deal was deleted."
    titles:
      connect_with_unlisters_network: "Connect with Unlisters Network"
      project_access_request: "Deal Access Request"
      incoming_requests: "Incoming Requests"
    texts:
      project_requested: "deal requested"
      link_to_deal: "link to deal"
      is_requesting_access_to_your_project: "is requesting access to your deal."
      would_like_to_connect_with_you: "would like to connect with you"
    labels:
      deal_requested: "deal requested:"
      message: "Message"
    confirmations:
      reject_request: "Are you sure you want to reject this request?"
      delete_project: "Are you sure you want to delete this deal?"
    uploads:
      statuses:
        preparing: "Preparing"
        pending: "Uploading"
        uploading: "Uploading"
        processing: "Processing"
        transferred: "Transfer"
        completed: "Complete!"
        failed: "Failed"
        cancelled: "Cancelled"
      actions:
        cancel: "Cancel"
        download: "Download"
      messages:
        file_uploads: "File Uploads"
        loading_file: "Loading file"
        select_files_first: "Please save the deal first before uploading files"
        upload_failed: "Upload failed"
        upload_successful: "uploaded successfully"
        too_many_files: "Maximum %{max} files allowed per upload"
        file_too_large: "%{filename} exceeds the maximum size of %{max_size}"
        unsupported_file_type: "%{filename} has unsupported file type: %{type}"
        rate_limit_exceeded: "Rate limit exceeded. Try again later."
    words:
      upload:
        one: "1 upload"
        other: "%{count} uploads"
  application:
    title: "Where Deals Happen"
    menu:
      projects: "Deals"
      all_projects: "All Deals"
      my_projects: "My Deals"
      wants: "Wants"
      all_wants: "All Wants"
      my_wants: "My Wants"
      network: "Network"
      unlisters: "Unlisters"
      my_connections: "My Network"
      invite_users: "Invite Members"
      subscriptions: "Subscriptions"
      my_profile: "My Profile"
      profile: "Profile"
      logout: "Logout"
    
  models:
    attributes:
      common:
        name: "Name"
        description: "Description"
        location: "Location"
        created_at: "Created"
        updated_at: "Updated"
    
    project:
      one: "Deal"
      other: "Deals"
      attributes:
        summary: "Title"
        full_description: "Full Description"
        project_type: "Deal Type"        
        category: "Category"
        subcategory: "Subcategory"
        price_value: "Price"
        price_currency: "Currency"
        price_text: "Price Text"
        land_area: "Area"
        area_unit: "Unit"
        commission: "Commission"
        commission_type: "Commission Type"
      actions:
        request_access: "Request access"
        show: "Show this deal"

      project_types:
        real_estate: "Real Estate"
        financing: "Financing"
        business: "Business"
        intellectual_property: "Intellectual Property"
        special: "Special"
        marketing_partnerships: "Marketing Partnerships"
        talent: "Talent"
        other: "Other"

      categories:
        commercial_property: "Commercial Property"
        land: "Land"
        homes: "Homes"
        offices: "Offices"
        real_estate: "Real Estate"
        business_acquisition: "Business Acquisition"
        private_equity: "Private Equity"
        franchise: "Franchise"
        investment_network: "Investment Network"
        patent: "Patent"
        trademark: "Trademark"
        copyright: "Copyright"
        technology: "Technology"
        art_work: "Art Work"
        collectibles: "Collectibles"
        estate_collection: "Estate Collection"
        performance_marketing: "Performance Marketing"
        affiliate_programs: "Affiliate Programs"
        channel_partners: "Channel Partners"
        white_label_reseller: "White Label Reseller"
        strategic_sales_partners: "Strategic Sales Partners"
        talent_network: "Talent Network"
        uncategorized: "Uncategorized"
        financing_real_estate: "Real Estate"
        financing_other: "Other"

      subcategories:
        industrial_building: "Industrial Building"
        warehouse: "Warehouse"
        commercial_premises: "Commercial Premises"
        other_building: "Other Building"
        developed: "Developed"
        buildable: "Buildable"
        non_building: "Non-Building"
        flat: "Flat"
        house: "House"
        office: "Office"
        asset_purchase: "Asset Purchase"
        business_purchase: "Business Purchase"
        startup: "Startup"
        expansion_funding_round: "Expansion Funding Round"
        family_office: "Family Office"
        general: "General"
        unspecified: "Unspecified"
      show:
        sharing:
          title_only: "Title Only"
          everything: "Everything"
          everyone: "/Everyone"
          my_network: "/My Network"
          full_details: "Full details"
        restricted_access: "There is restricted access to this deal. Request access from the owner, to see full details."
        location: "Location:"
        category: "Category:"
        project_owner: "Deal Owner:"
        last_update: "Update:"
        request_access: "Request access"

    user_profile:
      one: "Profile"
      other: "Profiles"
      project_owner: "Deal Owner"
      attributes:
        first_name: "First name"
        last_name: "Last name"
        bio: "Bio"
        phone: "Phone"
        city: "City"
        country: "Country"
    
    want:
      one: "Want"
      other: "Wants"
      attributes:
        summary: "Want Summary"
        place: "Location"
        want_type: "Want Type"
        category: "Category"
        subcategory: "Subcategory"
        price_min: "Min Price"
        price_max: "Max Price"
        price_currency: "Currency"
        description: "Description"
        notification: "Notify me about similar opportunities"
        created_at: "Created"
        updated_at: "Updated"

  projects:
    common:
        title_only: "Title Only"
        everything: "Everything"
        everyone: "/Everyone"
        my_network: "/My Network"
        full_details: "Full Details"
        pending: "pending"
        request_access: "Request Access"
        segments: 'Segments'
        no_title: "No title"
        file_deleted: "File successfully deleted."
        files_deleted:
          one: "%{count} file deleted successfully"
          other: "%{count} files deleted successfully"
        project_deleted: "Deal was deleted."
        offer_not_available: "The requested offer is not available."
    my:
      title: "My Deals"
      add: "Add New Deal"
    new: 
      title: "New Deal"
      default_draft_title: "Draft"
      draft_created: "New draft created. Start adding details."
    create:
      draft_created: "Draft created. You can now add files and complete the details."
    update:
      submitted_for_approval: "Deal submitted for approval."
      validation_failed: "Please complete all required fields before publishing."
      draft_saved: "Draft saved."
    form:
      validation_failed_details: "Cannot publish: %{errors}"
    edit: "Edit Deal"
    index:
      title: "All Deals"
      empty: "Start here by adding a new deal."
      filters:
        project_type_placeholder: "Deal Type"
        category_placeholder: "Category"
        subcategory_placeholder: "Subcategory"
        search_placeholder: "Search deals..."
        location_placeholder: "Location"
        radius: "Radius"
        all: "All"
        pending: "Pending"
        full_access: "Full Access"
        apply: "Apply"
        clear: "Clear"
      radius_options:
        km_20: "20km"
        km_50: "50km"
        km_100: "100km"
        km_300: "300km"
      project_list:
        no_projects: "No deals found matching your criteria."
      project_item:
        title_only: "Title"
        everything: "Everything"
        everyone: "/Everyone"
        my_network: "/My Network"
        full_details: "Full details"
        pending: "Pending"
        request_access: "Request access"
        delete_request: "Delete Request"
        request: "request"

    form:
      project_status: "Deal status"
      published: "Published"
      draft: "Draft"
      draft_info: "Nobody can see it."
      visibility: "Visibility"
      project_type_placeholder: "Select deal type"
      location_placeholder: "Enter location"
      category_placeholder: "Select category"
      subcategory_placeholder: "Select subcategory"
      settings: "Settings"
      delete_confirm: "Are you sure you want to delete this deal?"
      download: "Download"
      secure_download: "Secure download."
      large_files_warning: "Larger files might take longer to download."
      thank_you_patience: "Thank you for your patience"
      attached_files: "Attached files"
      no_files: "No files attached."
      title_placeholder: "Deal title or summary"
      description_placeholder: "Full Description"
      characters: "characters"
      add_files: "Add files"
      file_restrictions: "(Max size: 10MB, Accepted formats: pdf, png, jpg, jpeg, tiff)"
      manage_files: "Manage files"
      drop_zone:
        primary_text: "Drop files here or click to browse"
        supported_files: "Supported: PDF, Images"
        max_size: "Maximum size: 10MB per file"
        max_upload: "up to 5 files at once"
        upload_note: "For slow internet connections and large files, consider uploading one file at a time"
      active_uploads:
        one: "is being processed in the background"
        other: "are being processed in the background"
      stuck_uploads:
        one: "appears stuck (no progress for 30+ minutes)"
        other: "appear stuck (no progress for 30+ minutes)"
      started_time_ago: "Started %{time} ago"
      cleanup_stuck_uploads: "Cleanup Stuck Uploads"
      cleaning_up: "Cleaning up..."
      cleanup_failed_alert: "Failed to cleanup stuck uploads. Please try again."
      delete_file_confirm: "Are you sure you want to delete this file?"
      attached_files: "Attached files"
      delete_file: "Delete"
      select_all: "Select All"
      deselect_all: "Deselect All"
      delete_selected: "Delete Selected"
      no_files_selected: "No files selected"
      delete_files_confirm: "Are you sure you want to delete %{count} file(s)?"
      deleting: "Deleting..."
      delete_failed: "Failed to delete files"
      upload_notice: "Note: Create the deal first to enable file uploads"
      draft_description: "Complete required fields to publish."
      pending_approval: "Pending Approval"
      pending_description: "Your deal is submitted and awaiting admin approval."
      published_description: "Your deal is live and visible to members."
      update: "Save"
      preview: "Preview"
      publish: "Publish"
      share:
        what: "What to share"
        who: "Who to share with"
        title_only: "Title Only"
        everything: "Everything"
        everyone: "Everyone"
        my_network: "My Network"
        project_access: "Deal Access"
        who_can_discover: "Who can discover this deal?"
        what_can_see: "What can they see initially?"
        must_request: "Must request details"
        immediate_access: "Immediate access"
        secure: "Hidden deal"
        very_secure: "Hidden deal"
        moderate: "Partially open deal"
        open: "Open deal"
        network_approval: "Only My Network can discover this deal and full details require approval."
        network_direct: "Only My Network can discover this deal and see full details immediately."
        public_approval: "Everyone can discover this deal, but full details require approval."
        public_direct: "Everyone can discover this deal and they see full details immediately."
      last_update: "Last update"
      no_files: "No files attached"
      connected_users: "Connected members"
      no_connected_users: "No connected members"
    full_details_users:
      remove_access_confirm: "Remove access to this deal?"

  network_connections:
    title: "Unlisters Network"
    my_connections_title: "My Network"
    filters:
      not_in_network: "Not in My Network"
      show_all: "Show All"
      network_only: "My Network Only"
      location_placeholder: "Location"
      name_placeholder: "Name"
      search: "Search"
    status:
      self: "that's you"
      connected: "Connected"
      pending: "Pending"
    actions:
      connect: "Connect"
      disconnect: "Disconnect"
      show_more: "Show more"
      add_profile_name: "Add your profile name"
      find_connections: "Find connections"
      invite_connections: "Invite connections"
    messages:
      no_profiles: "No member profiles found."
      profile_warning: "Before you could send a connection request, please add your profile name."
      disconnect_confirm: "Are you sure you want to disconnect?"

  connection_requests:
    auth_title: "Deal Authorization Requests"
    actions:
      show_more: "Show more"
      grant_access: "Grant Deal Access"
    confirmations:
      reject: "Are you sure you want to reject this request?"
    create:
      success: "Connection request sent!"
      error: "Unable to send connection request."
    accept:
      success: "Connection request approved!"
      error: "Unable to approve connection request."
      already_connected: "You are already connected with this member."
    decline:
      success: "Connection request declined!"
      error: "Unable to decline connection request."
    destroy:
      success: "Request deleted."
      error: "Unable to delete request."

  invitations:
    invite_prompt: "Can't find them? Invite!"
    email_placeholder: 'Email'
    send_button: "Send Invitation"
    people_invited: "People You've Invited"
    status:
      accepted: 'Accepted'
    sent_label: 'Sent:'
    resend_button: 'Resend Invitation'
    no_invitations: 'No invitations sent.'
    create:
      success: "Invitation sent!"

  user_profiles:
    edit:
      title: "Edit Profile"
      complete_profile_required: "Please complete all required fields to unlock access to the application."
      approval_pending: "Your account is awaiting approval."
      visibility_notice: "Profiles are visible to the Unlisters Network"
      fill_profile_warning: "Please fill your profile info so you can find more people in the network."
      change_password: "Change Password"
      characters_minimum: "Minimum number of characters"
      leave_blank: "Leave blank if you don't want to change it"
      current_password: "We need your current password to confirm your changes"
      update_password: "Update password"
      back: "Back"
      success: "Member profile was successfully updated."
      personalize_experience: "Fill out your profile to unlock unique off-market deals."
      phone_placeholder: "Start typing: 421 9XX XXX XXX"
    show:
      title: "Your profile"
      visibility_notice: "Your profile is visible to Unlisters Network"
      edit_profile: "Edit profile"
      change_password: "Change Password"
      email: "Email"
      hidden: "hidden"

  activerecord:
    errors:
      models:
        project:
          attributes:
            summary:
              blank: "Title can't be empty"
              too_long: "is too long (maximum is 123 characters)"
            location:
              blank: "Location is required for published deals"
            project_type:
              blank: "Deal type must be selected"
            category:
              blank: "Category must be selected"
            subcategory:
              blank: "Subcategory must be selected"
              invalid: "is not valid for the selected category"
            price_currency:
              invalid_currency: "%{value} is not a valid currency"
            commission_type:
              invalid_type: "%{value} is not a valid commission type"
            approved:
              permission_denied: "status can only be changed by an admin"
            base:
              sharing_options_required: "Either 'Title Only' or 'Everything' must be selected"
              visibility_required: "Either 'My Network' or 'Everyone' must be selected"
              sharing_consistency: "Choose either 'My Network' or 'Everyone' for visibility"
              detail_consistency: "Choose either 'Title Only' or 'Everything' for sharing level"
        want:
          attributes:
            price_max:
              greater_than_min: "must be greater than or equal to minimum price"


  wants:
    common:
      no_title: "Untitled Want"
      want_created: "Want created successfully."
      want_updated: "Want updated successfully."
      want_deleted: "Want was deleted."
      draft: "Draft"
    
    my:
      title: "My Wants"
      add: "Add New Want"
    
    new:
      title: "New Want"
      default_draft_title: "Draft Want"
    
    edit:
      title: "Edit Want"
    
    create:
      success: "Want created successfully."
      error: "Unable to create want."
    
    update:
      success: "Want updated successfully."
      error: "Unable to update want."
    
    destroy:
      success: "Want deleted successfully."
      error: "Unable to delete want."
    
    index:
      title: "Investment Wants"
      empty: "Start here by adding a new want."
      filters:
        want_type_placeholder: "Want Type"
        category_placeholder: "Category"
        subcategory_placeholder: "Subcategory"
        search_placeholder: "Search wants..."
        location_placeholder: "Location"
        apply: "Apply"
        clear: "Clear"
      want_list:
        no_wants: "No wants found matching your criteria."
    
    form:
      summary_placeholder: "Describe what you are looking for..."
      description_placeholder: "Detailed description of your requirements and preferences..."
      place_placeholder: "Where are you looking?"
      want_type_placeholder: "Select Want Type"
      category_placeholder: "Select Category"
      subcategory_placeholder: "Select Subcategory"
      characters: "characters"
      save: "Save Want"
      update: "Update Want"
      delete_confirm: "Are you sure you want to delete this want?"
    
    show:
      title: "Want Title"
      location: "Location:"
      category: "Category:"
      want_owner: "Want Owner:"
      last_update: "Update:"
      contact_owner: "Contact Owner"

  user_approval_request_notification:
    subject: "New Member Awaiting Approval"
    title: "New Member Awaiting Approval"
    user_registered_line: "A new member has completed their registration and is awaiting approval."
    user_details: "Member Details:"
    email_label: "Email:"
    name_label: "Name:"
    review_instruction_line: "Please visit the admin dashboard to review and approve this member."
    view_admin_dashboard_link: "Go to Admin Dashboard"