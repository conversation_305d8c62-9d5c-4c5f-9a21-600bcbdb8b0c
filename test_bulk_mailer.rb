#!/usr/bin/env rails runner
# Test script for bulk mail simulation

puts "=" * 60
puts "TESTING BULK MAIL SIMULATION"
puts "=" * 60

# Find or create a test project
user = User.first || User.create!(
  email: "<EMAIL>",
  password: "password123",
  confirmed_at: Time.current
)

project = Project.first || Project.create!(
  user: user,
  summary: "Test Project for Bulk Mail",
  project_type: "development",
  category: "homes",
  subcategory: "flat"
)

# Get some test users (creating them if needed)
test_users = []
existing_users = User.where.not(id: user.id).limit(20).to_a
test_users.concat(existing_users)

# Create additional test users if needed
if test_users.size < 20
  (20 - test_users.size).times do |i|
    test_users << User.create!(
      email: "test_user_#{i + existing_users.size}@example.com",
      password: "password123",
      confirmed_at: Time.current
    )
  end
end

puts "Project: #{project.summary} (ID: #{project.id})"
puts "Test Users: #{test_users.size}"
puts "-" * 60

# Clear any existing trackers for this test
EmailDeliveryTracker.where(project: project).destroy_all
puts "Cleared existing delivery trackers"

# Test 1: Small batch (should use regular mode)
puts "\n📧 TEST 1: Small batch (5 users) - Should use REGULAR mode"
small_batch = test_users.first(5)
BulkNotificationJob.new.perform(project, small_batch.map(&:id))

# Test 2: Large batch (should use simulation)
puts "\n📧 TEST 2: Large batch (#{test_users.size} users) - Should use SIMULATION mode"
BulkNotificationJob.new.perform(project, test_users.map(&:id))

# Test 3: Duplicate send (should skip already sent)
puts "\n📧 TEST 3: Duplicate send - Should skip already sent emails"
BulkNotificationJob.new.perform(project, test_users.map(&:id))

puts "\n" + "=" * 60
puts "✅ BULK MAIL SIMULATION TEST COMPLETE"
puts "=" * 60