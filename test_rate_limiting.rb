#!/usr/bin/env ruby
# ABOUTME: Test script to verify email rate limiting is working properly
# ABOUTME: Creates test data and sends bulk emails to verify rate-limited delivery

require_relative 'config/environment'

Rails.logger = Logger.new(STDOUT)
Rails.logger.level = Logger::INFO

puts "=" * 60
puts "RATE LIMITING TEST"
puts "=" * 60
puts "This test will create a project and send emails to 5 test users"
puts "With proper rate limiting, emails should be queued and sent ~0.5-1.5 seconds apart"
puts ""

# Create test users
test_emails = [
  "<EMAIL>",
  "<EMAIL>", 
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>"
]

puts "Creating test users..."
users = test_emails.map do |email|
  User.find_or_create_by!(email: email) do |u|
    u.password = "password123"
    u.confirmed_at = Time.current
  end
end

# Create a test project
puts "Creating test project..."
owner = users.first
project = Project.create!(
  user: owner,
  summary: "Test Project for Rate Limiting #{Time.current}",
  is_approved: true,
  visibility: :network_only
)

puts ""
puts "Queueing bulk notification job with #{users.size} recipients..."
puts "Expected behavior: Emails should be sent with ~1.5 second intervals"
puts ""

# Queue the bulk notification
BulkNotificationJob.perform_later(project, users.map(&:id))

puts "Job queued! Check the logs to see rate limiting in action."
puts ""
puts "To monitor in real-time, run in another terminal:"
puts "  tail -f log/development.log | grep -E '(EMAIL|Rate|Resend|delivery)'"
puts ""
puts "To check GoodJob queue status:"
puts "  rails c"
puts "  GoodJob::Job.where(queue_name: 'mailers').count"
puts ""