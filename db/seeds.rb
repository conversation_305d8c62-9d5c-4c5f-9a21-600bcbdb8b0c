# This file should contain all the record creation needed to seed the database with its default values.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

puts "🌱 Seeding database for #{Rails.env} environment..."

# Load the correct seeds file for our Rails environment
# This is the Rails-standard approach for environment-specific seeding
load(Rails.root.join('db', 'seeds', "#{Rails.env.downcase}.rb"))