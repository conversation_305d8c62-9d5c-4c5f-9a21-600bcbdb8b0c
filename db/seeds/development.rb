# Development environment seeds - test data for local development

puts "Creating development environment data..."

# Create subscription plans (needed in all environments)
puts "Creating subscription plans..."
plans_data = [
  { name: "Standard Monthly", tier: "standard", interval: "month" },
  { name: "Standard Yearly", tier: "standard", interval: "year" },
  { name: "Pilot Monthly", tier: "pilot", interval: "month" },
  { name: "Pilot Yearly", tier: "pilot", interval: "year" }
]

plans_data.each do |plan_data|
  Plan.find_or_create_by!(name: plan_data[:name]) do |plan|
    plan.tier = plan_data[:tier]
    plan.interval = plan_data[:interval]
    plan.active = true
    puts "  ✓ Created plan: #{plan.name}"
  end
end

# Create admin user for development
puts "Creating admin user..."
AdminUser.find_or_create_by!(email: '<EMAIL>') do |admin|
  admin.password = 'password'
  admin.password_confirmation = 'password'
  puts "  ✓ Created admin user: #{admin.email}"
end

# Create 10 testing users
puts "Creating test users..."
10.times do |i|
  email = "user#{i}@example.com"
  
  # Skip if user already exists
  next if User.exists?(email: email)
  
  user = User.create!(
    email: email,
    password: "password",
    password_confirmation: "password"
  )
  
  UserProfile.create!(
    user: user,
    first_name: ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Charlie", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"].sample,
    last_name: ["<PERSON> <PERSON>.", "<PERSON>", "<PERSON> <PERSON>.", "<PERSON> II", "<PERSON>", "<PERSON> V", "<PERSON> <PERSON>.", "<PERSON> <PERSON>", "<PERSON> Sr.", "<PERSON> <PERSON>"].sample,
    bio: [
      "<PERSON>ate developer with a love for creating innovative solutions.",
      "Experienced project manager with a knack for team leadership.",
      "Creative designer with a keen eye for detail and aesthetics.",
      "Data scientist with a strong background in statistical analysis.",
      "Marketing expert with a talent for crafting compelling campaigns.",
      "Seasoned entrepreneur with a track record of successful startups.",
      "Software engineer with a focus on scalable web applications.",
      "Product manager with a strategic approach to product development.",
      "UX/UI designer dedicated to enhancing user experiences.",
      "Business analyst with expertise in market research and data analysis."
    ].sample,
    location: ["Kosice", "Poprad", "Italy", "Germany", "Great Britain", "Moldova", "Romania", "Lipany"].sample
  )

  # Create associated projects for each user
  3.times do |j|
    category = Project.categories.keys.sample
    subcategory = Project::CATEGORIES[category.to_sym].sample
    Project.create!(
      user: user,
      summary: [
        "Innovative app to streamline workflow and increase productivity.",
        "A cutting-edge platform for seamless team collaboration.",
        "Revolutionary tool for efficient project management.",
        "Next-gen solution for data-driven decision making.",
        "User-friendly interface for enhanced user experience.",
        "Robust system for secure data storage and retrieval.",
        "Advanced analytics tool for market trend analysis.",
        "Comprehensive suite for end-to-end project tracking.",
        "Scalable architecture for growing business needs.",
        "Intuitive design for effortless navigation and use."
      ].sample, 
      location: ["New York", "Los Angeles", "Chicago", "Houston", "Phoenix", "Philadelphia", "San Antonio", "San Diego", "Dallas", "San Jose"].sample,
      category: category,
      subcategory: subcategory,
      summary_only: true,
      semi_public: true
    )
  end
  
  puts "  ✓ Created user: #{user.email} with profile and projects"
end

# Create connection requests to the first user
if User.count > 1
  puts "Creating connection requests..."
  main_user = User.first
  User.where.not(id: main_user.id).limit(5).each do |user|
    # Skip if connection request already exists
    next if ConnectionRequest.exists?(inviter: user, invitee: main_user)
    
    ConnectionRequest.create!(
      inviter: user,
      invitee: main_user,
      status: 0, # Pending
      message: "Hello! I would like to connect with you to discuss potential collaboration opportunities. I have been following your work and I am impressed with your projects. Let's connect and explore how we can work together to achieve our goals. Looking forward to hearing from you soon. Best regards."
    )
    puts "  ✓ Created connection request from #{user.email} to #{main_user.email}"
  end
end

puts "✅ Development environment seeding completed!"
