# Production environment seeds - only essential data for production

puts "Creating production environment data..."

# Create subscription plans (essential for the application to function)
puts "Creating subscription plans..."
plans_data = [
  { name: "Standard Yearly", tier: "standard", interval: "month" },
  { name: "Standard Annual", tier: "standard", interval: "year" },
  { name: "Pilot Monthly", tier: "pilot", interval: "month" },
  { name: "Pilot Yearly", tier: "pilot", interval: "year" }
]

plans_data.each do |plan_data|
  Plan.find_or_create_by!(name: plan_data[:name]) do |plan|
    plan.tier = plan_data[:tier]
    plan.interval = plan_data[:interval]
    plan.active = true
    puts "  ✓ Created plan: #{plan.name}"
  end
end

# You might want to create essential admin users here if needed
# But be very careful about hardcoded credentials in production
# Consider using environment variables for sensitive data

# Example (commented out for security):
# if ENV['ADMIN_EMAIL'].present? && ENV['ADMIN_PASSWORD'].present?
#   AdminUser.find_or_create_by!(email: ENV['ADMIN_EMAIL']) do |admin|
#     admin.password = ENV['ADMIN_PASSWORD']
#     admin.password_confirmation = ENV['ADMIN_PASSWORD']
#     puts "  ✓ Created production admin user"
#   end
# end

puts "✅ Production environment seeding completed!"
puts "⚠️  Note: No test data created in production environment"
