# Test environment seeds - minimal data needed for testing

puts "Creating test environment data..."

# Create subscription plans (needed for testing subscription features)
puts "Creating subscription plans..."
plans_data = [
  { name: "Free", tier: "free", interval: "one_time", price_cents: 0 },
  { name: "Premium Monthly", tier: "premium", interval: "month", price_cents: 999 },
  { name: "Premium Annual", tier: "premium", interval: "year", price_cents: 9999 },
  { name: "Premium Lifetime", tier: "premium", interval: "one_time", price_cents: 29999 },
  { name: "Pilot Monthly", tier: "pilot", interval: "month", price_cents: 0 },
  { name: "Pilot Annual", tier: "pilot", interval: "year", price_cents: 0 },
  { name: "Pilot Lifetime", tier: "pilot", interval: "one_time", price_cents: 0 }
]

plans_data.each do |plan_data|
  Plan.find_or_create_by!(name: plan_data[:name]) do |plan|
    plan.tier = plan_data[:tier]
    plan.interval = plan_data[:interval]
    plan.price_cents = plan_data[:price_cents]
    plan.active = true
    puts "  ✓ Created plan: #{plan.name}"
  end
end

# Create a minimal admin user for testing
AdminUser.find_or_create_by!(email: '<EMAIL>') do |admin|
  admin.password = 'password'
  admin.password_confirmation = 'password'
  puts "  ✓ Created test admin user"
end

# Create a few test users for testing purposes
3.times do |i|
  email = "test-user#{i}@example.com"
  
  # Skip if user already exists
  next if User.exists?(email: email)
  
  user = User.create!(
    email: email,
    password: "password",
    password_confirmation: "password"
  )
  
  UserProfile.create!(
    user: user,
    first_name: "Test",
    last_name: "User#{i}",
    bio: "Test user for automated testing",
    location: "Test Location"
  )
  
  puts "  ✓ Created test user: #{user.email}"
end

puts "✅ Test environment data created successfully!"
