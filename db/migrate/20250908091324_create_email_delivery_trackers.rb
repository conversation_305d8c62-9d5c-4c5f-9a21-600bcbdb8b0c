class CreateEmailDeliveryTrackers < ActiveRecord::Migration[7.0]
  def change
    create_table :email_delivery_trackers do |t|
      t.references :project, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.string :email_type, null: false
      t.datetime :sent_at
      t.integer :retry_count, default: 0
      t.string :status, default: 'pending'
      t.timestamps
      
      t.index [:project_id, :user_id, :email_type], unique: true, name: 'idx_email_trackers_on_project_user_type'
    end
  end
end
