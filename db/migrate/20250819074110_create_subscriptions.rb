# ABOUTME: Creates the subscriptions table to track user subscription history with full Stripe compatibility
# ABOUTME: Supports multiple payment providers and maintains complete subscription history for analytics
class CreateSubscriptions < ActiveRecord::Migration[7.0]
  def change
    create_table :subscriptions do |t|
      t.references :user, null: false, foreign_key: true, index: true
      t.references :plan, null: false, foreign_key: true
      
      # Status matching Stripe exactly
      t.integer :status, null: false, default: 0
      # active: 0, trialing: 1, past_due: 2, canceled: 3, 
      # unpaid: 4, incomplete: 5, incomplete_expired: 6
      
      # Billing periods (Stripe fields)
      t.datetime :current_period_start
      t.datetime :current_period_end
      t.datetime :trial_start
      t.datetime :trial_end
      t.datetime :canceled_at
      t.datetime :ended_at
      
      # Stripe sync fields
      t.string :stripe_subscription_id
      t.string :stripe_customer_id
      t.string :stripe_status # Store original Stripe status string
      
      # Alternative providers
      t.string :paddle_subscription_id
      t.string :lemonsqueezy_subscription_id
      
      # Payment tracking
      t.integer :payment_provider, default: 3 # 'stripe': 0, 'paddle': 1, 'lemonsqueezy': 2, 'manual': 3, 'referral': 4
      t.json :provider_data # Full provider response cached
      
      # Metadata
      t.string :cancel_reason
      t.boolean :cancel_at_period_end, default: false
      
      t.timestamps
    end

    add_index :subscriptions, :stripe_subscription_id, unique: true
    add_index :subscriptions, [:user_id, :status]
    add_index :subscriptions, [:user_id, :created_at]
    add_index :subscriptions, :status
    add_index :subscriptions, :current_period_end
  end
end