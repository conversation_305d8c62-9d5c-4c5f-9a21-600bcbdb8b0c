# ABOUTME: Creates the plans table to store subscription plan configurations for Stripe integration
# ABOUTME: Supports multiple payment providers (Stripe, Paddle, LemonSqueezy) with metadata storage
class CreatePlans < ActiveRecord::Migration[7.0]
  def change
    create_table :plans do |t|
      t.string :name, null: false
      t.integer :tier, null: false # free: 0, premium: 1, pilot: 2
      t.integer :interval, null: false # one_time: 0, month: 1, year: 2
      t.integer :price_cents, null: false, default: 0
      
      # Stripe sync fields
      t.string :stripe_price_id
      t.string :stripe_product_id
      
      # Alternative payment provider support
      t.string :paddle_price_id
      t.string :lemonsqueezy_variant_id
      
      t.boolean :active, default: true
      t.json :metadata # Store any provider-specific data
      t.timestamps
    end

    add_index :plans, :stripe_price_id, unique: true
    add_index :plans, :tier
    add_index :plans, :active
  end
end