# ABOUTME: Empty migration - originally intended to migrate user subscription data
# ABOUTME: Kept for schema_migrations consistency but no migration needed for existing users
class MigrateUserSubscriptionData < ActiveRecord::Migration[7.0]
  def up
    # No user subscription data migration needed:
    # - Free users: Don't need subscription records (detected by absence of subscription)
    # - Pilot users: Continue using existing tier_pilot? logic  
    # - Premium users: Don't exist in the database
    # Plans are created via db/seeds files, not migrations
  end

  def down
    # No-op migration, nothing to reverse
  end
end